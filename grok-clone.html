<!DOCTYPE html>
<html lang="en" translate="no" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grok - AI Assistant</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --sidebar-width: 16rem;
            --sidebar-width-icon: 3.5rem;
            --background: #ffffff;
            --surface-base: #ffffff;
            --surface-l2: #f8f9fa;
            --fg-primary: #0f1419;
            --fg-secondary: #536471;
            --fg-invert: #ffffff;
            --border-l1: #e1e8ed;
            --border-l2: #cfd9de;
            --button-primary: #1d9bf0;
            --button-primary-hover: #1a8cd8;
            --button-ghost-hover: #f7f9fa;
            --button-secondary-hover: #e1e8ed;
            --button-filled: #1d9bf0;
            --toggle-border: #cfd9de;
            --ring: #1d9bf0;
            --highlight: #e1f5fe;
            --text-secondary: #536471;
            --primary: #1d9bf0;
            --secondary: #536471;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--surface-base);
            color: var(--fg-primary);
            overflow-x: hidden;
            letter-spacing: -0.1px;
            line-height: 1.5;
        }

        .container {
            display: flex;
            min-height: 100vh;
            width: 100%;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100vh;
            position: relative;
        }

        /* Header Navigation */
        .header {
            position: relative;
            z-index: 25;
            flex-shrink: 0;
        }

        .nav-bar {
            height: 4rem;
            position: absolute;
            top: 0;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            background: linear-gradient(to bottom, var(--background) 0%, var(--background) 80%, transparent 100%);
        }

        .nav-left {
            position: absolute;
            left: 0.25rem;
            display: flex;
            gap: 0.25rem;
            align-items: center;
        }

        .nav-right {
            position: absolute;
            right: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .logo {
            margin-left: 0.5rem;
            margin-right: 0.125rem;
            border-radius: 0.5rem;
            opacity: 0.9;
            transition: opacity 0.2s;
        }

        .logo:hover {
            opacity: 1;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            white-space: nowrap;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            border: none;
            border-radius: 9999px;
            transition: all 0.1s;
            text-decoration: none;
        }

        .btn:focus-visible {
            outline: none;
            box-shadow: 0 0 0 1px var(--ring);
        }

        .btn-icon {
            height: 2.5rem;
            width: 2.5rem;
            background: transparent;
            color: var(--fg-primary);
            border: 1px solid transparent;
        }

        .btn-icon:hover {
            background-color: var(--button-ghost-hover);
        }

        .btn-primary {
            background-color: var(--button-primary);
            color: var(--fg-invert);
            height: 2rem;
            padding: 0.5rem 0.75rem;
        }

        .btn-primary:hover {
            background-color: var(--button-primary-hover);
        }

        .btn-secondary {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--toggle-border);
            height: 2rem;
            padding: 0.5rem 0.75rem;
        }

        .btn-secondary:hover {
            background-color: var(--button-secondary-hover);
        }

        /* Main Content Area */
        .content-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            height: 100%;
            padding: 0.5rem;
            margin: 0 auto;
            justify-content: center;
            margin-top: 4rem;
            overflow: auto;
        }

        .content-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
            height: 450px;
            width: 100%;
            padding-top: 3rem;
        }

        .logo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 0 0.5rem;
            gap: 1.5rem;
            max-width: 80%;
        }

        .main-logo {
            width: 320px;
            height: 64px;
        }

        /* Search Section */
        .search-section {
            position: absolute;
            bottom: 0;
            margin: 0 auto;
            left: 0;
            right: 0;
            max-width: 1200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            gap: 0.25rem;
        }

        .search-container {
            display: flex;
            flex-direction: column-reverse;
            align-items: center;
            justify-content: space-between;
            flex: 1;
            width: 100%;
            gap: 0;
            position: relative;
            padding: 0.5rem;
        }

        .search-form {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 10;
            margin-top: 0.5rem;
        }

        .search-input-container {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            width: 100%;
            position: relative;
            max-width: 80%;
        }

        .query-bar {
            background-color: var(--surface-l2);
            border: 1px solid var(--border-l1);
            position: relative;
            width: 100%;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            max-width: 1200px;
            border-radius: 1.5rem;
            padding-bottom: 3rem;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            transition: border-color 0.1s, box-shadow 0.1s;
        }

        .query-bar:hover {
            border-color: var(--border-l2);
        }

        .query-bar:focus-within {
            border-color: var(--border-l2);
        }

        .search-input-wrapper {
            position: relative;
            z-index: 10;
        }

        .search-placeholder {
            position: absolute;
            padding: 1.25rem 0.5rem;
            color: var(--fg-secondary);
            pointer-events: none;
            user-select: none;
        }

        .search-input {
            width: 100%;
            padding: 1.25rem 0.5rem;
            background: transparent;
            border: none;
            outline: none;
            color: var(--fg-primary);
            min-height: 3.5rem;
            margin: 0;
            margin-bottom: 1.25rem;
            resize: none;
            font-family: inherit;
            font-size: 1rem;
        }

        .search-controls {
            display: flex;
            gap: 0.375rem;
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            border: 2px solid transparent;
            padding: 0.5rem;
            max-width: 100%;
        }

        .attach-btn {
            border: 1px solid var(--border-l2);
            color: var(--fg-primary);
            height: 2.5rem;
            width: 2.5rem;
            border-radius: 9999px;
        }

        .attach-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .control-group {
            display: flex;
            flex: 1;
            gap: 0.375rem;
            max-width: 100%;
        }

        .mode-controls {
            flex: 1;
            display: flex;
            gap: 0.375rem;
            max-width: 100%;
        }

        .mode-toggle {
            display: flex;
            border: 1px solid var(--border-l2);
            border-radius: 9999px;
            align-items: center;
            max-height: 2.5rem;
            transition: all 0.1s;
            position: relative;
            overflow: hidden;
        }

        .mode-btn {
            height: 2.5rem;
            padding: 0.375rem 0.875rem;
            font-size: 0.875rem;
            border-radius: 9999px;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.1s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .mode-btn:hover {
            background-color: var(--button-ghost-hover);
        }

        .mode-divider {
            height: 1rem;
            width: 1px;
            background-color: var(--toggle-border);
        }

        .think-btn {
            border: 1px solid var(--border-l2);
            color: var(--fg-primary);
            height: 2.5rem;
            padding: 0.375rem 0.875rem;
            font-size: 0.875rem;
            border-radius: 9999px;
            background: transparent;
        }

        .think-btn:hover {
            background-color: var(--button-ghost-hover);
        }

        .model-selector {
            display: flex;
            align-items: center;
            height: 2.5rem;
            padding: 0.375rem 0.875rem;
            font-size: 0.875rem;
            border-radius: 9999px;
            border: 1px solid var(--border-l2);
            background: transparent;
            cursor: pointer;
        }

        .model-selector:hover {
            background-color: var(--button-ghost-hover);
        }

        .voice-btn {
            margin-left: auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
            border-radius: 9999px;
            height: 2.5rem;
            aspect-ratio: 1;
            align-items: center;
            gap: 0.125rem;
            background-color: var(--button-filled);
            color: var(--fg-invert);
            border: none;
            cursor: pointer;
        }

        .voice-bars {
            display: flex;
            align-items: center;
            gap: 0.125rem;
        }

        .voice-bar {
            width: 0.125rem;
            border-radius: 9999px;
            background-color: var(--fg-invert);
        }

        /* Feature Buttons */
        .features-section {
            width: 100%;
            display: flex;
            justify-content: center;
        }

        .features-container {
            z-index: 10;
            width: 100%;
            max-width: 80%;
            min-height: 2.5rem;
            max-width: 1200px;
            overflow: visible;
        }

        .features-buttons {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .feature-row {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
            gap: 0.5rem;
            justify-content: center;
            align-items: center;
        }

        .feature-btn {
            border: 1px solid var(--border-l2);
            color: var(--fg-primary);
            height: 2.5rem;
            padding: 0.375rem 0.875rem;
            font-size: 0.875rem;
            border-radius: 9999px;
            background: transparent;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .feature-btn:hover {
            background-color: var(--button-ghost-hover);
        }

        .feature-icon {
            color: var(--secondary);
        }

        /* Footer */
        .footer {
            font-size: 0.6875rem;
            color: var(--secondary);
            white-space: nowrap;
        }

        .footer a {
            color: var(--primary);
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --background: #000000;
                --surface-base: #000000;
                --surface-l2: #16181c;
                --fg-primary: #e7e9ea;
                --fg-secondary: #71767b;
                --fg-invert: #000000;
                --border-l1: #2f3336;
                --border-l2: #3e4144;
                --button-ghost-hover: #1d1f23;
                --button-secondary-hover: #2f3336;
            }
        }

        /* Loading animation */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .loading {
            animation: pulse 2s infinite;
        }

        /* Smooth transitions */
        * {
            transition: background-color 0.1s ease, border-color 0.1s ease, color 0.1s ease;
        }

        /* Focus styles */
        .btn:focus-visible,
        .search-input:focus-visible {
            outline: 2px solid var(--ring);
            outline-offset: 2px;
        }

        /* Hover effects */
        .logo:hover {
            transform: scale(1.02);
        }

        .feature-btn:hover .feature-icon {
            color: var(--fg-primary);
        }

        /* Responsive Design */
        @media (min-width: 640px) {
            .content-wrapper {
                padding: 1rem;
                gap: 2.25rem;
                margin-top: 0;
            }

            .search-section {
                position: relative;
                gap: 1.25rem;
                bottom: auto;
                left: auto;
                right: auto;
                max-width: 100%;
            }

            .search-container {
                flex-direction: column;
                gap: 0.75rem;
                padding: 0;
            }

            .search-input-container {
                max-width: 80%;
            }

            .query-bar {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }

            .search-placeholder {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }

            .search-input {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }

            .features-container {
                display: block;
            }

            .footer {
                font-size: 0.75rem;
            }
        }

        @media (min-width: 1280px) {
            .logo-section {
                max-width: 80%;
            }

            .search-input-container {
                max-width: 80%;
            }

            .features-container {
                max-width: 80%;
            }
        }

        /* Additional animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .content-container {
            animation: fadeIn 0.6s ease-out;
        }

        /* Improved button states */
        .btn:active {
            transform: scale(0.98);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn:disabled:hover {
            background-color: transparent;
        }

        /* Icons */
        .icon {
            width: 1.125rem;
            height: 1.125rem;
            stroke-width: 2;
        }

        .icon-sm {
            width: 0.75rem;
            height: 0.75rem;
        }

        /* Hidden elements for mobile */
        .hidden-sm {
            display: none;
        }

        @media (min-width: 640px) {
            .hidden-sm {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <main class="main-content">
            <!-- Header Navigation -->
            <div class="header">
                <div class="nav-bar">
                    <div class="nav-left">
                        <div>
                            <a href="#" class="logo" aria-label="Home page">
                                <svg width="88" height="33" viewBox="0 0 88 33" fill="none" xmlns="http://www.w3.org/2000/svg" class="logo">
                                    <path d="M76.4462 24.7077V8.41584H79.0216V19.1679L84.4685 12.9109H87.5908L82.6908 18.2731L87.6364 24.7077H84.5596L80.5539 19.1788L79.0216 19.1679V24.7077H76.4462Z" fill="currentColor"></path>
                                    <path d="M68.6362 24.9815C64.8074 24.9815 62.7335 22.2662 62.7335 18.7979C62.7335 15.3068 64.8074 12.6143 68.6362 12.6143C72.4878 12.6143 74.5389 15.3068 74.5389 18.7979C74.5389 22.2662 72.4878 24.9815 68.6362 24.9815ZM65.4228 18.7979C65.4228 21.4904 66.8813 22.8366 68.6362 22.8366C70.4139 22.8366 71.8497 21.4904 71.8497 18.7979C71.8497 16.1054 70.4139 14.7363 68.6362 14.7363C66.8813 14.7363 65.4228 16.1054 65.4228 18.7979Z" fill="currentColor"></path>
                                    <path d="M55.5659 24.7077V14.782L57.731 12.9109H62.3347V15.1014H58.1413V24.7077H55.5659Z" fill="currentColor"></path>
                                    <path d="M45.7187 25.009C40.8101 25.009 37.8834 21.4448 37.8834 16.5846C37.8834 11.6788 40.9146 8.02795 45.8145 8.02795C49.6433 8.02795 52.4466 9.99027 53.1075 13.6411H50.1675C49.7345 11.5647 48.0024 10.401 45.8145 10.401C42.282 10.401 40.7322 13.4586 40.7322 16.5846C40.7322 19.7106 42.282 22.7454 45.8145 22.7454C49.1875 22.7454 50.6689 20.3039 50.7828 18.2731H45.7006V15.9105H53.381L53.3684 17.1457C53.3684 21.7359 51.4978 25.009 45.7187 25.009Z" fill="currentColor"></path>
                                    <path d="M13.2371 21.0407L24.3186 12.8506C24.8619 12.4491 25.6384 12.6057 25.8973 13.2294C27.2597 16.5185 26.651 20.4712 23.9403 23.1851C21.2297 25.8989 17.4581 26.4941 14.0108 25.1386L10.2449 26.8843C15.6463 30.5806 22.2053 29.6665 26.304 25.5601C29.5551 22.3051 30.562 17.8683 29.6205 13.8673L29.629 13.8758C28.2637 7.99809 29.9647 5.64871 33.449 0.844576C33.5314 0.730667 33.6139 0.616757 33.6964 0.5L29.1113 5.09055V5.07631L13.2343 21.0436" fill="currentColor"></path>
                                    <path d="M10.9503 23.0313C7.07343 19.3235 7.74185 13.5853 11.0498 10.2763C13.4959 7.82722 17.5036 6.82767 21.0021 8.2971L24.7595 6.55998C24.0826 6.07017 23.215 5.54334 22.2195 5.17313C17.7198 3.31926 12.3326 4.24192 8.67479 7.90126C5.15635 11.4239 4.0499 16.8403 5.94992 21.4622C7.36924 24.9165 5.04257 27.3598 2.69884 29.826C1.86829 30.7002 1.0349 31.5745 0.36364 32.5L10.9474 23.0341" fill="currentColor"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                    
                    <div class="nav-right">
                        <button class="btn btn-icon" type="button" aria-label="History">
                            <svg width="20" height="20" viewBox="2 2 21 21" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon">
                                <path d="M3 5L19 5" stroke="currentColor" stroke-linecap="square" stroke-linejoin="round"></path>
                                <path d="M3 12H7" stroke="currentColor" stroke-linecap="square" stroke-linejoin="round"></path>
                                <circle cx="16" cy="15" r="4" stroke="currentColor"></circle>
                                <path d="M19 18L21 20" stroke="currentColor" stroke-linecap="square"></path>
                                <path d="M3 19H7" stroke="currentColor" stroke-linecap="square" stroke-linejoin="round"></path>
                            </svg>
                        </button>
                        
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <button class="btn btn-icon hidden-sm" type="button" aria-label="Settings">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon">
                                    <path stroke="currentColor" d="M13.5 3h-3C9.408 5.913 8.024 6.711 4.956 6.201l-1.5 2.598c1.976 2.402 1.976 4 0 6.402l1.5 2.598c3.068-.51 4.452.288 5.544 3.201h3c1.092-2.913 2.476-3.711 5.544-3.2l1.5-2.599c-1.976-2.402-1.976-4 0-6.402l-1.5-2.598c-3.068.51-4.452-.288-5.544-3.201Z"></path>
                                    <circle cx="12" cy="12" r="2.5" fill="currentColor"></circle>
                                </svg>
                            </button>
                            
                            <button class="btn btn-primary" type="button">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                                <div>Sign up</div>
                            </button>
                            
                            <button class="btn btn-secondary hidden-sm" type="button">
                                <div>Sign in</div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="content-wrapper">
                <div class="content-container">
                    <div class="logo-section">
                        <svg class="main-logo" viewBox="0 0 88 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M76.4462 24.7077V8.41584H79.0216V19.1679L84.4685 12.9109H87.5908L82.6908 18.2731L87.6364 24.7077H84.5596L80.5539 19.1788L79.0216 19.1679V24.7077H76.4462Z" fill="currentColor"></path>
                            <path d="M68.6362 24.9815C64.8074 24.9815 62.7335 22.2662 62.7335 18.7979C62.7335 15.3068 64.8074 12.6143 68.6362 12.6143C72.4878 12.6143 74.5389 15.3068 74.5389 18.7979C74.5389 22.2662 72.4878 24.9815 68.6362 24.9815ZM65.4228 18.7979C65.4228 21.4904 66.8813 22.8366 68.6362 22.8366C70.4139 22.8366 71.8497 21.4904 71.8497 18.7979C71.8497 16.1054 70.4139 14.7363 68.6362 14.7363C66.8813 14.7363 65.4228 16.1054 65.4228 18.7979Z" fill="currentColor"></path>
                            <path d="M55.5659 24.7077V14.782L57.731 12.9109H62.3347V15.1014H58.1413V24.7077H55.5659Z" fill="currentColor"></path>
                            <path d="M45.7187 25.009C40.8101 25.009 37.8834 21.4448 37.8834 16.5846C37.8834 11.6788 40.9146 8.02795 45.8145 8.02795C49.6433 8.02795 52.4466 9.99027 53.1075 13.6411H50.1675C49.7345 11.5647 48.0024 10.401 45.8145 10.401C42.282 10.401 40.7322 13.4586 40.7322 16.5846C40.7322 19.7106 42.282 22.7454 45.8145 22.7454C49.1875 22.7454 50.6689 20.3039 50.7828 18.2731H45.7006V15.9105H53.381L53.3684 17.1457C53.3684 21.7359 51.4978 25.009 45.7187 25.009Z" fill="currentColor"></path>
                            <path d="M13.2371 21.0407L24.3186 12.8506C24.8619 12.4491 25.6384 12.6057 25.8973 13.2294C27.2597 16.5185 26.651 20.4712 23.9403 23.1851C21.2297 25.8989 17.4581 26.4941 14.0108 25.1386L10.2449 26.8843C15.6463 30.5806 22.2053 29.6665 26.304 25.5601C29.5551 22.3051 30.562 17.8683 29.6205 13.8673L29.629 13.8758C28.2637 7.99809 29.9647 5.64871 33.449 0.844576C33.5314 0.730667 33.6139 0.616757 33.6964 0.5L29.1113 5.09055V5.07631L13.2343 21.0436" fill="currentColor"></path>
                            <path d="M10.9503 23.0313C7.07343 19.3235 7.74185 13.5853 11.0498 10.2763C13.4959 7.82722 17.5036 6.82767 21.0021 8.2971L24.7595 6.55998C24.0826 6.07017 23.215 5.54334 22.2195 5.17313C17.7198 3.31926 12.3326 4.24192 8.67479 7.90126C5.15635 11.4239 4.0499 16.8403 5.94992 21.4622C7.36924 24.9165 5.04257 27.3598 2.69884 29.826C1.86829 30.7002 1.0349 31.5745 0.36364 32.5L10.9474 23.0341" fill="currentColor"></path>
                        </svg>
                    </div>

                    <div class="search-section">
                        <div class="search-container">
                            <form class="search-form">
                                <div class="search-input-container">
                                    <input type="file" multiple style="display: none;" name="files">
                                    <div class="query-bar">
                                        <div class="search-input-wrapper">
                                            <span class="search-placeholder">What do you want to know?</span>
                                            <textarea class="search-input" aria-label="Ask Grok anything"></textarea>
                                        </div>
                                        <div class="search-controls">
                                            <button class="btn attach-btn" type="button" aria-label="Please login to attach files" disabled>
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon">
                                                    <path d="M10 9V15C10 16.1046 10.8954 17 12 17V17C13.1046 17 14 16.1046 14 15V7C14 4.79086 12.2091 3 10 3V3C7.79086 3 6 4.79086 6 7V15C6 18.3137 8.68629 21 12 21V21C15.3137 21 18 18.3137 18 15V8" stroke="currentColor"></path>
                                                </svg>
                                            </button>
                                            
                                            <div class="control-group">
                                                <div class="mode-controls">
                                                    <div class="mode-toggle">
                                                        <button class="mode-btn" type="button" aria-label="DeepSearch">
                                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon feature-icon">
                                                                <path d="M2 13.8236C4.5 22.6927 18 21.3284 18 14.0536C18 9.94886 11.9426 9.0936 10.7153 11.1725C9.79198 12.737 14.208 12.6146 13.2847 14.1791C12.0574 16.2581 6 15.4029 6 11.2982C6 3.68585 20.5 2.2251 22 11.0945" stroke="currentColor"></path>
                                                            </svg>
                                                            <span>DeepSearch</span>
                                                        </button>
                                                        <div class="mode-divider"></div>
                                                        <button class="mode-btn" type="button" aria-label="Change mode">
                                                            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-sm feature-icon">
                                                                <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-linecap="square"></path>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                    
                                                    <button class="btn think-btn" type="button" aria-label="Think">
                                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon feature-icon">
                                                            <path d="M15 16.1378L14.487 15.2794L14 15.5705V16.1378H15ZM8.99997 16.1378H9.99997V15.5705L9.51293 15.2794L8.99997 16.1378ZM18 9C18 11.4496 16.5421 14.0513 14.487 15.2794L15.5129 16.9963C18.1877 15.3979 20 12.1352 20 9H18ZM12 4C13.7598 4 15.2728 4.48657 16.3238 5.33011C17.3509 6.15455 18 7.36618 18 9H20C20 6.76783 19.082 4.97946 17.5757 3.77039C16.0931 2.58044 14.1061 2 12 2V4ZM5.99997 9C5.99997 7.36618 6.64903 6.15455 7.67617 5.33011C8.72714 4.48657 10.2401 4 12 4V2C9.89382 2 7.90681 2.58044 6.42427 3.77039C4.91791 4.97946 3.99997 6.76783 3.99997 9H5.99997ZM9.51293 15.2794C7.4578 14.0513 5.99997 11.4496 5.99997 9H3.99997C3.99997 12.1352 5.81225 15.3979 8.48701 16.9963L9.51293 15.2794ZM9.99997 19.5001V16.1378H7.99997V19.5001H9.99997ZM10.5 20.0001C10.2238 20.0001 9.99997 19.7763 9.99997 19.5001H7.99997C7.99997 20.8808 9.11926 22.0001 10.5 22.0001V20.0001ZM13.5 20.0001H10.5V22.0001H13.5V20.0001ZM14 19.5001C14 19.7763 13.7761 20.0001 13.5 20.0001V22.0001C14.8807 22.0001 16 20.8808 16 19.5001H14ZM14 16.1378V19.5001H16V16.1378H14Z" fill="currentColor"></path>
                                                            <path d="M9 16.0001H15" stroke="currentColor"></path>
                                                            <path d="M12 16V12" stroke="currentColor" stroke-linecap="square"></path>
                                                        </svg>
                                                        <span>Think</span>
                                                    </button>
                                                </div>
                                                
                                                <div style="display: flex; align-items: center;">
                                                    <button class="model-selector" type="button">
                                                        <span style="font-size: 0.75rem; color: var(--primary);">Grok 3</span>
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-sm feature-icon">
                                                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-linecap="square"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <div style="margin-left: auto; display: flex; align-items: end; gap: 0.25rem;">
                                                <button class="voice-btn" aria-label="Enter voice mode">
                                                    <div class="voice-bars">
                                                        <div class="voice-bar" style="height: 0.4rem;"></div>
                                                        <div class="voice-bar" style="height: 0.8rem;"></div>
                                                        <div class="voice-bar" style="height: 1.2rem;"></div>
                                                        <div class="voice-bar" style="height: 0.7rem;"></div>
                                                        <div class="voice-bar" style="height: 1rem;"></div>
                                                        <div class="voice-bar" style="height: 0.4rem;"></div>
                                                    </div>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            
                            <div class="features-section">
                                <div class="features-container">
                                    <div class="features-buttons">
                                        <div class="feature-row">
                                            <button class="btn feature-btn" type="button">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon feature-icon">
                                                    <path d="M12 4H8C5.79086 4 4 5.79086 4 8V16C4 18.2091 5.79086 20 8 20H16C18.2091 20 20 18.2091 20 16V12" stroke="currentColor"></path>
                                                    <path d="M4 15.3333L8 12L16.4706 20" stroke="currentColor"></path>
                                                    <circle cx="14" cy="10" r="1.75" fill="currentColor"></circle>
                                                    <path d="M21.0355 5.49989L18.5 5.49989M18.5 5.49989L15.9645 5.49989M18.5 5.49989L18.5 2.96436M18.5 5.49989L18.5 8.03542" stroke="currentColor" stroke-linecap="square"></path>
                                                </svg>
                                                <p>Create Images</p>
                                            </button>
                                            
                                            <button class="btn feature-btn" type="button">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon feature-icon">
                                                    <path d="M9 4V4C8.07069 4 7.60603 4 7.21964 4.07686C5.63288 4.39249 4.39249 5.63288 4.07686 7.21964C4 7.60603 4 8.07069 4 9V9M15 4V4C15.9293 4 16.394 4 16.7804 4.07686C18.3671 4.39249 19.6075 5.63288 19.9231 7.21964C20 7.60603 20 8.07069 20 9V9M9 20V20C8.07069 20 7.60603 20 7.21964 19.9231C5.63288 19.6075 4.39249 18.3671 4.07686 16.7804C4 16.394 4 15.9293 4 15V15M15 20V20C15.9293 20 16.394 20 16.7804 19.9231C18.3671 19.6075 19.6075 18.3671 19.9231 16.7804C20 16.394 20 15.9293 20 15V15" stroke="currentColor"></path>
                                                    <circle cx="11.5" cy="11.5" r="2.5" stroke="currentColor"></circle>
                                                    <path d="M13.5 13.5L15.5 15.5" stroke="currentColor"></path>
                                                </svg>
                                                <p>Research</p>
                                            </button>
                                            
                                            <button class="btn feature-btn" type="button" aria-label="Sign in to edit images">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon feature-icon">
                                                    <path d="M10.75 9.375C10.75 10.1344 10.1344 10.75 9.375 10.75C8.61561 10.75 8 10.1344 8 9.375C8 8.61561 8.61561 8 9.375 8C10.1344 8 10.75 8.61561 10.75 9.375Z" fill="currentColor"></path>
                                                    <path d="M7.15104 15.1655L9.67016 12.3665C9.85307 12.1633 10.1653 12.1446 10.3711 12.3247L12 13.75L13.4519 11.5918C13.6454 11.3041 14.0661 11.296 14.2706 11.5761L16.9198 15.2052C17.161 15.5356 16.925 16 16.516 16L7.52269 16C7.0898 16 6.86145 15.4873 7.15104 15.1655Z" fill="currentColor"></path>
                                                    <path d="M9 4V4C8.07069 4 7.60603 4 7.21964 4.07686C5.63288 4.39249 4.39249 5.63288 4.07686 7.21964C4 7.60603 4 8.07069 4 9V9M15 4V4C15.9293 4 16.394 4 16.7804 4.07686C18.3671 4.39249 19.6075 5.63288 19.9231 7.21964C20 7.60603 20 8.07069 20 9V9M9 20V20C8.07069 20 7.60603 20 7.21964 19.9231C5.63288 19.6075 4.39249 18.3671 4.07686 16.7804C4 16.394 4 15.9293 4 15V15M15 20V20C15.9293 20 16.394 20 16.7804 19.9231C18.3671 19.6075 19.6075 18.3671 19.9231 16.7804C20 16.394 20 15.9293 20 15V15" stroke="currentColor"></path>
                                                </svg>
                                                <span>Edit Image</span>
                                            </button>
                                            
                                            <button class="btn feature-btn" type="button">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feature-icon">
                                                    <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"></path>
                                                    <path d="M18 14h-8"></path>
                                                    <path d="M15 18h-5"></path>
                                                    <path d="M10 6h8v4h-8V6Z"></path>
                                                </svg>
                                                Latest News
                                            </button>
                                            
                                            <button class="btn feature-btn" type="button">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feature-icon">
                                                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                                    <circle cx="12" cy="7" r="4"></circle>
                                                </svg>
                                                Personas
                                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-sm feature-icon">
                                                    <path d="m6 9 6 6 6-6"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="footer">
                            By messaging Grok, you agree to our <a href="https://x.ai/legal/terms-of-service" target="_blank" rel="noopener noreferrer">Terms</a> and <a href="https://x.ai/legal/privacy-policy" target="_blank" rel="noopener noreferrer">Privacy Policy</a>.
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Enhanced interactivity
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('.search-input');
            const placeholder = document.querySelector('.search-placeholder');
            const queryBar = document.querySelector('.query-bar');

            // Handle placeholder visibility
            function updatePlaceholder() {
                if (searchInput.value.trim() || document.activeElement === searchInput) {
                    placeholder.style.display = 'none';
                } else {
                    placeholder.style.display = 'block';
                }
            }

            searchInput.addEventListener('input', updatePlaceholder);
            searchInput.addEventListener('focus', updatePlaceholder);
            searchInput.addEventListener('blur', updatePlaceholder);

            // Auto-resize textarea
            searchInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 200) + 'px';
            });

            // Enhanced query bar focus effects
            searchInput.addEventListener('focus', function() {
                queryBar.style.borderColor = 'var(--border-l2)';
                queryBar.style.boxShadow = '0 0 0 1px var(--ring)';
            });

            searchInput.addEventListener('blur', function() {
                queryBar.style.borderColor = 'var(--border-l1)';
                queryBar.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.05)';
            });

            // Button interactions with ripple effect
            document.querySelectorAll('.btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Voice button animation
            const voiceBars = document.querySelectorAll('.voice-bar');
            let animationInterval;

            document.querySelector('.voice-btn').addEventListener('mouseenter', function() {
                animationInterval = setInterval(() => {
                    voiceBars.forEach((bar, index) => {
                        const height = Math.random() * 1.2 + 0.4;
                        bar.style.height = height + 'rem';
                        bar.style.transition = 'height 0.1s ease';
                    });
                }, 150);
            });

            document.querySelector('.voice-btn').addEventListener('mouseleave', function() {
                clearInterval(animationInterval);
                // Reset to original heights
                const originalHeights = ['0.4rem', '0.8rem', '1.2rem', '0.7rem', '1rem', '0.4rem'];
                voiceBars.forEach((bar, index) => {
                    bar.style.height = originalHeights[index];
                    bar.style.transition = 'height 0.3s ease';
                });
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Focus search input with Ctrl+K or Cmd+K
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    searchInput.focus();
                }

                // Submit with Ctrl+Enter or Cmd+Enter
                if ((e.ctrlKey || e.metaKey) && e.key === 'Enter' && document.activeElement === searchInput) {
                    e.preventDefault();
                    handleSubmit();
                }
            });

            // Handle form submission
            function handleSubmit() {
                const query = searchInput.value.trim();
                if (query) {
                    // Simulate loading state
                    const submitBtn = document.querySelector('.voice-btn');
                    submitBtn.style.opacity = '0.7';
                    submitBtn.style.cursor = 'not-allowed';

                    // Show loading animation
                    voiceBars.forEach(bar => {
                        bar.classList.add('loading');
                    });

                    // Reset after 2 seconds (simulate response)
                    setTimeout(() => {
                        submitBtn.style.opacity = '1';
                        submitBtn.style.cursor = 'pointer';
                        voiceBars.forEach(bar => {
                            bar.classList.remove('loading');
                        });

                        // Clear input and show success message
                        searchInput.value = '';
                        updatePlaceholder();
                        showNotification('Query submitted successfully!');
                    }, 2000);
                }
            }

            // Notification system
            function showNotification(message) {
                const notification = document.createElement('div');
                notification.textContent = message;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: var(--button-primary);
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 1000;
                    animation: slideIn 0.3s ease;
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }

            // Theme toggle (hidden feature)
            let clickCount = 0;
            document.querySelector('.main-logo').addEventListener('click', function() {
                clickCount++;
                if (clickCount === 5) {
                    document.documentElement.classList.toggle('dark');
                    clickCount = 0;
                    showNotification('Theme toggled!');
                }
                setTimeout(() => clickCount = 0, 2000);
            });

            // Feature button interactions
            document.querySelectorAll('.feature-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const feature = this.textContent.trim();
                    showNotification(`${feature} feature clicked!`);
                });
            });
        });

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }

            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }

            .dark {
                --background: #000000;
                --surface-base: #000000;
                --surface-l2: #16181c;
                --fg-primary: #e7e9ea;
                --fg-secondary: #71767b;
                --fg-invert: #000000;
                --border-l1: #2f3336;
                --border-l2: #3e4144;
                --button-ghost-hover: #1d1f23;
                --button-secondary-hover: #2f3336;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
