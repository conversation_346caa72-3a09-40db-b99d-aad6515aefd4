# Grok.com Clone

这是一个使用纯 HTML、CSS 和 JavaScript 完全复制 grok.com 前端页面的项目。

## 功能特性

### ✅ 已实现的功能

1. **完整的页面结构**
   - 导航栏（包含 Logo、历史、设置、登录/注册按钮）
   - 主要内容区域（大 Logo、搜索框、功能按钮）
   - 页脚（条款和隐私政策链接）

2. **主题系统**
   - 支持浅色/深色主题切换
   - 使用 CSS 变量实现主题管理
   - 主题偏好保存到 localStorage

3. **响应式设计**
   - 移动端适配
   - 桌面端和移动端不同的布局
   - 灵活的组件尺寸调整

4. **交互功能**
   - 搜索框自动调整高度
   - 下拉菜单（Personas）
   - 按钮悬停效果
   - 键盘快捷键（/ 键聚焦搜索框）
   - 主题切换（点击设置按钮）

5. **UI 组件**
   - 搜索框（带占位符文本）
   - 功能按钮（Create Images、Research、Edit Image、Latest News、Personas）
   - 模式切换按钮（DeepSearch、Think）
   - 模型选择器（Grok 3）
   - 语音输入按钮

6. **样式系统**
   - 类似 Tailwind CSS 的工具类
   - CSS 变量用于颜色、间距、圆角等
   - 平滑的过渡动画
   - 自定义滚动条样式

## 文件结构

```
grok-clone/
├── index.html          # 主 HTML 文件
├── styles.css          # 所有样式
├── script.js           # JavaScript 交互逻辑
├── implementation-plan.md  # 实施计划文档
└── README.md          # 本文件
```

## 使用方法

1. 直接在浏览器中打开 `index.html` 文件
2. 点击设置按钮切换主题
3. 使用 `/` 键快速聚焦搜索框
4. 点击各个功能按钮查看交互效果

## 技术亮点

- **纯前端实现**：无需任何构建工具或框架
- **模块化 JavaScript**：使用类来组织代码
- **CSS 变量**：实现灵活的主题系统
- **语义化 HTML**：良好的可访问性
- **响应式设计**：适配各种设备

## 浏览器兼容性

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 未来改进

- 添加更多动画效果
- 实现搜索功能的后端集成
- 添加更多主题选项
- 优化移动端体验
- 添加更多键盘快捷键

## 许可证

本项目仅用于学习和演示目的。