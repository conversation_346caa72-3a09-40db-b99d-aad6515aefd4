# Grok.com 前端页面复制计划

## 项目概述
使用纯 HTML、CSS 和 JavaScript 完全复制 grok.com 的前端页面。

## 技术栈
- HTML5
- CSS3 (包含自定义 CSS 变量和 Tailwind-like 工具类)
- 原生 JavaScript
- SVG 图标

## 页面结构分析

### 1. 主题系统
- 支持浅色/深色模式
- CSS 变量用于颜色管理
- 类名：`light` / `dark`

### 2. 布局结构
```
├── 导航栏
│   ├── Logo (左侧)
│   ├── 操作按钮 (右侧)
│   │   ├── 历史按钮
│   │   ├── 设置按钮
│   │   ├── Sign up 按钮
│   │   └── Sign in 按钮
│   └── 渐变背景效果
├── 主内容区
│   ├── Grok Logo (大)
│   ├── 搜索框区域
│   │   ├── 输入框 ("What do you want to know?")
│   │   ├── 附件按钮
│   │   ├── DeepSearch 切换
│   │   ├── Think 切换
│   │   ├── 模型选择 (Grok 3)
│   │   └── 语音输入按钮
│   └── 功能按钮组
│       ├── Create Images
│       ├── Research
│       ├── Edit Image
│       ├── Latest News
│       └── Personas
└── 页脚
    └── 条款和隐私政策链接

```

### 3. 关键样式特性
- 响应式设计 (@container 查询)
- 圆角按钮和输入框
- 悬停效果和过渡动画
- 阴影效果
- 自定义滚动条
- 渐变背景

### 4. 交互功能
- 下拉菜单
- 按钮悬停状态
- 输入框焦点状态
- 模态框/弹出菜单

### 5. 动画效果
- SVG 加载动画
- 过渡效果
- 按钮状态变化

## 实现步骤

### 第一阶段：基础结构
1. 创建 index.html
2. 创建 styles.css
3. 创建 script.js
4. 设置基本的 HTML 结构

### 第二阶段：样式系统
1. 实现 CSS 变量系统
2. 创建工具类（类似 Tailwind）
3. 实现响应式布局
4. 添加主题切换功能

### 第三阶段：组件开发
1. 导航栏组件
2. 搜索框组件
3. 按钮组件
4. 下拉菜单组件

### 第四阶段：交互和动画
1. 实现下拉菜单功能
2. 添加悬停效果
3. 实现焦点状态
4. 添加过渡动画

### 第五阶段：优化和测试
1. 跨浏览器兼容性测试
2. 响应式设计测试
3. 性能优化
4. 代码清理

## 注意事项
- 保持代码整洁和模块化
- 确保所有交互都能正常工作
- 注意细节，如阴影、边框、间距等
- 确保响应式设计在各种设备上都能正常显示