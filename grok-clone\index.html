<!DOCTYPE html>
<html lang="en" translate="no" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>rok Clone</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="antialiased">
    <div class="app-wrapper">
        <!-- Navigation Bar -->
        <nav class="navbar">
            <div class="nav-container">
                <!-- Logo -->
                <div class="nav-logo">
                    <a href="/" aria-label="Home page" class="logo-link">
                        <svg width="88" height="33" viewBox="0 0 88 33" fill="none" xmlns="http://www.w3.org/2000/svg" class="logo-svg">
                            <path d="M76.4462 24.7077V8.41584H79.0216V19.1679L84.4685 12.9109H87.5908L82.6908 18.2731L87.6364 24.7077H84.5596L80.5539 19.1788L79.0216 19.1679V24.7077H76.4462Z" fill="currentColor"></path>
                            <path d="M68.6362 24.9815C64.8074 24.9815 62.7335 22.2662 62.7335 18.7979C62.7335 15.3068 64.8074 12.6143 68.6362 12.6143C72.4878 12.6143 74.5389 15.3068 74.5389 18.7979C74.5389 22.2662 72.4878 24.9815 68.6362 24.9815ZM65.4228 18.7979C65.4228 21.4904 66.8813 22.8366 68.6362 22.8366C70.4139 22.8366 71.8497 21.4904 71.8497 18.7979C71.8497 16.1054 70.4139 14.7363 68.6362 14.7363C66.8813 14.7363 65.4228 16.1054 65.4228 18.7979Z" fill="currentColor"></path>
                            <path d="M55.5659 24.7077V14.782L57.731 12.9109H62.3347V15.1014H58.1413V24.7077H55.5659Z" fill="currentColor"></path>
                            <path d="M45.7187 25.009C40.8101 25.009 37.8834 21.4448 37.8834 16.5846C37.8834 11.6788 40.9146 8.02795 45.8145 8.02795C49.6433 8.02795 52.4466 9.99027 53.1075 13.6411H50.1675C49.7345 11.5647 48.0024 10.401 45.8145 10.401C42.282 10.401 40.7322 13.4586 40.7322 16.5846C40.7322 19.7106 42.282 22.7454 45.8145 22.7454C49.1875 22.7454 50.6689 20.3039 50.7828 18.2731H45.7006V15.9105H53.381L53.3684 17.1457C53.3684 21.7359 51.4978 25.009 45.7187 25.009Z" fill="currentColor"></path>
                            <path d="M13.2371 21.0407L24.3186 12.8506C24.8619 12.4491 25.6384 12.6057 25.8973 13.2294C27.2597 16.5185 26.651 20.4712 23.9403 23.1851C21.2297 25.8989 17.4581 26.4941 14.0108 25.1386L10.2449 26.8843C15.6463 30.5806 22.2053 29.6665 26.304 25.5601C29.5551 22.3051 30.562 17.8683 29.6205 13.8673L29.629 13.8758C28.2637 7.99809 29.9647 5.64871 33.449 0.844576C33.5314 0.730667 33.6139 0.616757 33.6964 0.5L29.1113 5.09055V5.07631L13.2343 21.0436" fill="currentColor" id="mark"></path>
                            <path d="M10.9503 23.0313C7.07343 19.3235 7.74185 13.5853 11.0498 10.2763C13.4959 7.82722 17.5036 6.82767 21.0021 8.2971L24.7595 6.55998C24.0826 6.07017 23.215 5.54334 22.2195 5.17313C17.7198 3.31926 12.3326 4.24192 8.67479 7.90126C5.15635 11.4239 4.0499 16.8403 5.94992 21.4622C7.36924 24.9165 5.04257 27.3598 2.69884 29.826C1.86829 30.7002 1.0349 31.5745 0.36364 32.5L10.9474 23.0341" fill="currentColor" id="mark"></path>
                        </svg>
                    </a>
                </div>

                <!-- Nav Actions -->
                <div class="nav-actions">
                    <!-- History Button -->
                    <button class="btn-icon" aria-label="History">
                        <svg width="20" height="20" viewBox="2 2 21 21" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon">
                            <path d="M3 5L19 5" stroke="currentColor" stroke-linecap="square" stroke-linejoin="round"></path>
                            <path d="M3 12H7" stroke="currentColor" stroke-linecap="square" stroke-linejoin="round"></path>
                            <circle cx="16" cy="15" r="4" stroke="currentColor"></circle>
                            <path d="M19 18L21 20" stroke="currentColor" stroke-linecap="square"></path>
                            <path d="M3 19H7" stroke="currentColor" stroke-linecap="square" stroke-linejoin="round"></path>
                        </svg>
                    </button>

                    <!-- Settings Button -->
                    <button class="btn-icon desktop-only" aria-label="Settings">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon">
                            <path stroke="currentColor" d="M13.5 3h-3C9.408 5.913 8.024 6.711 4.956 6.201l-1.5 2.598c1.976 2.402 1.976 4 0 6.402l1.5 2.598c3.068-.51 4.452.288 5.544 3.201h3c1.092-2.913 2.476-3.711 5.544-3.2l1.5-2.599c-1.976-2.402-1.976-4 0-6.402l-1.5-2.598c-3.068.51-4.452-.288-5.544-3.201Z"></path>
                            <circle cx="12" cy="12" r="2.5" fill="currentColor"></circle>
                        </svg>
                    </button>

                    <!-- Sign Up Button -->
                    <button class="btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        <span>Sign up</span>
                    </button>

                    <!-- Sign In Button -->
                    <button class="btn-secondary desktop-only">
                        <span>Sign in</span>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-wrapper">
                <!-- Large Grok Logo -->
                <div class="logo-container">
                    <svg width="320" height="64" viewBox="0 0 88 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M76.4462 24.7077V8.41584H79.0216V19.1679L84.4685 12.9109H87.5908L82.6908 18.2731L87.6364 24.7077H84.5596L80.5539 19.1788L79.0216 19.1679V24.7077H76.4462Z" fill="currentColor"></path>
                        <path d="M68.6362 24.9815C64.8074 24.9815 62.7335 22.2662 62.7335 18.7979C62.7335 15.3068 64.8074 12.6143 68.6362 12.6143C72.4878 12.6143 74.5389 15.3068 74.5389 18.7979C74.5389 22.2662 72.4878 24.9815 68.6362 24.9815ZM65.4228 18.7979C65.4228 21.4904 66.8813 22.8366 68.6362 22.8366C70.4139 22.8366 71.8497 21.4904 71.8497 18.7979C71.8497 16.1054 70.4139 14.7363 68.6362 14.7363C66.8813 14.7363 65.4228 16.1054 65.4228 18.7979Z" fill="currentColor"></path>
                        <path d="M55.5659 24.7077V14.782L57.731 12.9109H62.3347V15.1014H58.1413V24.7077H55.5659Z" fill="currentColor"></path>
                        <path d="M45.7187 25.009C40.8101 25.009 37.8834 21.4448 37.8834 16.5846C37.8834 11.6788 40.9146 8.02795 45.8145 8.02795C49.6433 8.02795 52.4466 9.99027 53.1075 13.6411H50.1675C49.7345 11.5647 48.0024 10.401 45.8145 10.401C42.282 10.401 40.7322 13.4586 40.7322 16.5846C40.7322 19.7106 42.282 22.7454 45.8145 22.7454C49.1875 22.7454 50.6689 20.3039 50.7828 18.2731H45.7006V15.9105H53.381L53.3684 17.1457C53.3684 21.7359 51.4978 25.009 45.7187 25.009Z" fill="currentColor"></path>
                        <path d="M13.2371 21.0407L24.3186 12.8506C24.8619 12.4491 25.6384 12.6057 25.8973 13.2294C27.2597 16.5185 26.651 20.4712 23.9403 23.1851C21.2297 25.8989 17.4581 26.4941 14.0108 25.1386L10.2449 26.8843C15.6463 30.5806 22.2053 29.6665 26.304 25.5601C29.5551 22.3051 30.562 17.8683 29.6205 13.8673L29.629 13.8758C28.2637 7.99809 29.9647 5.64871 33.449 0.844576C33.5314 0.730667 33.6139 0.616757 33.6964 0.5L29.1113 5.09055V5.07631L13.2343 21.0436" fill="currentColor" id="mark"></path>
                        <path d="M10.9503 23.0313C7.07343 19.3235 7.74185 13.5853 11.0498 10.2763C13.4959 7.82722 17.5036 6.82767 21.0021 8.2971L24.7595 6.55998C24.0826 6.07017 23.215 5.54334 22.2195 5.17313C17.7198 3.31926 12.3326 4.24192 8.67479 7.90126C5.15635 11.4239 4.0499 16.8403 5.94992 21.4622C7.36924 24.9165 5.04257 27.3598 2.69884 29.826C1.86829 30.7002 1.0349 31.5745 0.36364 32.5L10.9474 23.0341" fill="currentColor" id="mark"></path>
                    </svg>
                </div>

                <!-- Search Area -->
                <div class="search-container">
                    <form class="search-form">
                        <div class="search-box">
                            <div class="search-input-wrapper">
                                <span class="search-placeholder">What do you want to know?</span>
                                <textarea 
                                    class="search-input" 
                                    aria-label="Ask Grok anything"
                                    rows="1"
                                ></textarea>
                            </div>

                            <!-- Search Box Actions -->
                            <div class="search-actions">
                                <!-- Attach Button -->
                                <button type="button" class="btn-icon-bordered" aria-label="Attach files" disabled>
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-secondary" stroke-width="2">
                                        <path d="M10 9V15C10 16.1046 10.8954 17 12 17V17C13.1046 17 14 16.1046 14 15V7C14 4.79086 12.2091 3 10 3V3C7.79086 3 6 4.79086 6 7V15C6 18.3137 8.68629 21 12 21V21C15.3137 21 18 18.3137 18 15V8" stroke="currentColor"></path>
                                    </svg>
                                </button>

                                <!-- Mode Toggles -->
                                <div class="mode-toggles">
                                    <div class="toggle-group">
                                        <button type="button" class="toggle-btn toggle-left" aria-label="DeepSearch" aria-pressed="false">
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-secondary" stroke-width="2">
                                                <path d="M2 13.8236C4.5 22.6927 18 21.3284 18 14.0536C18 9.94886 11.9426 9.0936 10.7153 11.1725C9.79198 12.737 14.208 12.6146 13.2847 14.1791C12.0574 16.2581 6 15.4029 6 11.2982C6 3.68585 20.5 2.2251 22 11.0945" stroke="currentColor"></path>
                                            </svg>
                                            <span>DeepSearch</span>
                                        </button>
                                        <div class="toggle-divider"></div>
                                        <button type="button" class="toggle-btn toggle-right" aria-label="Change mode">
                                            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-secondary" stroke-width="2">
                                                <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-linecap="square"></path>
                                            </svg>
                                        </button>
                                    </div>

                                    <button type="button" class="btn-bordered" aria-label="Think" aria-pressed="false">
                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-secondary" stroke-width="2">
                                            <path d="M15 16.1378L14.487 15.2794L14 15.5705V16.1378H15ZM8.99997 16.1378H9.99997V15.5705L9.51293 15.2794L8.99997 16.1378ZM18 9C18 11.4496 16.5421 14.0513 14.487 15.2794L15.5129 16.9963C18.1877 15.3979 20 12.1352 20 9H18ZM12 4C13.7598 4 15.2728 4.48657 16.3238 5.33011C17.3509 6.15455 18 7.36618 18 9H20C20 6.76783 19.082 4.97946 17.5757 3.77039C16.0931 2.58044 14.1061 2 12 2V4ZM5.99997 9C5.99997 7.36618 6.64903 6.15455 7.67617 5.33011C8.72714 4.48657 10.2401 4 12 4V2C9.89382 2 7.90681 2.58044 6.42427 3.77039C4.91791 4.97946 3.99997 6.76783 3.99997 9H5.99997ZM9.51293 15.2794C7.4578 14.0513 5.99997 11.4496 5.99997 9H3.99997C3.99997 12.1352 5.81225 15.3979 8.48701 16.9963L9.51293 15.2794ZM9.99997 19.5001V16.1378H7.99997V19.5001H9.99997ZM10.5 20.0001C10.2238 20.0001 9.99997 19.7763 9.99997 19.5001H7.99997C7.99997 20.8808 9.11926 22.0001 10.5 22.0001V20.0001ZM13.5 20.0001H10.5V22.0001H13.5V20.0001ZM14 19.5001C14 19.7763 13.7761 20.0001 13.5 20.0001V22.0001C14.8807 22.0001 16 20.8808 16 19.5001H14ZM14 16.1378V19.5001H16V16.1378H14Z" fill="currentColor"></path>
                                            <path d="M9 16.0001H15" stroke="currentColor"></path>
                                            <path d="M12 16V12" stroke="currentColor" stroke-linecap="square"></path>
                                        </svg>
                                        <span>Think</span>
                                    </button>
                                </div>

                                <!-- Model Selector -->
                                <button type="button" class="btn-model">
                                    <span class="model-name">Grok 3</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-chevron" stroke-width="2">
                                        <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-linecap="square"></path>
                                    </svg>
                                </button>

                                <!-- Voice Button -->
                                <button type="button" class="btn-voice" aria-label="Enter voice mode">
                                    <div class="voice-bars">
                                        <div class="voice-bar" style="height: 0.4rem;"></div>
                                        <div class="voice-bar" style="height: 0.8rem;"></div>
                                        <div class="voice-bar" style="height: 1.2rem;"></div>
                                        <div class="voice-bar" style="height: 0.7rem;"></div>
                                        <div class="voice-bar" style="height: 1rem;"></div>
                                        <div class="voice-bar" style="height: 0.4rem;"></div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Feature Buttons -->
                    <div class="feature-buttons">
                        <button class="btn-feature">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-secondary">
                                <path d="M12 4H8C5.79086 4 4 5.79086 4 8V16C4 18.2091 5.79086 20 8 20H16C18.2091 20 20 18.2091 20 16V12" stroke="currentColor"></path>
                                <path d="M4 15.3333L8 12L16.4706 20" stroke="currentColor"></path>
                                <circle cx="14" cy="10" r="1.75" fill="currentColor"></circle>
                                <path d="M21.0355 5.49989L18.5 5.49989M18.5 5.49989L15.9645 5.49989M18.5 5.49989L18.5 2.96436M18.5 5.49989L18.5 8.03542" stroke="currentColor" stroke-linecap="square"></path>
                            </svg>
                            <span>Create Images</span>
                        </button>

                        <button class="btn-feature">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-secondary">
                                <path d="M9 4V4C8.07069 4 7.60603 4 7.21964 4.07686C5.63288 4.39249 4.39249 5.63288 4.07686 7.21964C4 7.60603 4 8.07069 4 9V9M15 4V4C15.9293 4 16.394 4 16.7804 4.07686C18.3671 4.39249 19.6075 5.63288 19.9231 7.21964C20 7.60603 20 8.07069 20 9V9M9 20V20C8.07069 20 7.60603 20 7.21964 19.9231C5.63288 19.6075 4.39249 18.3671 4.07686 16.7804C4 16.394 4 15.9293 4 15V15M15 20V20C15.9293 20 16.394 20 16.7804 19.9231C18.3671 19.6075 19.6075 18.3671 19.9231 16.7804C20 16.394 20 15.9293 20 15V15" stroke="currentColor"></path>
                                <circle cx="11.5" cy="11.5" r="2.5" stroke="currentColor"></circle>
                                <path d="M13.5 13.5L15.5 15.5" stroke="currentColor"></path>
                            </svg>
                            <span>Research</span>
                        </button>

                        <button class="btn-feature">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-secondary">
                                <path d="M10.75 9.375C10.75 10.1344 10.1344 10.75 9.375 10.75C8.61561 10.75 8 10.1344 8 9.375C8 8.61561 8.61561 8 9.375 8C10.1344 8 10.75 8.61561 10.75 9.375Z" fill="currentColor"></path>
                                <path d="M7.15104 15.1655L9.67016 12.3665C9.85307 12.1633 10.1653 12.1446 10.3711 12.3247L12 13.75L13.4519 11.5918C13.6454 11.3041 14.0661 11.296 14.2706 11.5761L16.9198 15.2052C17.161 15.5356 16.925 16 16.516 16L7.52269 16C7.0898 16 6.86145 15.4873 7.15104 15.1655Z" fill="currentColor"></path>
                                <path d="M9 4V4C8.07069 4 7.60603 4 7.21964 4.07686C5.63288 4.39249 4.39249 5.63288 4.07686 7.21964C4 7.60603 4 8.07069 4 9V9M15 4V4C15.9293 4 16.394 4 16.7804 4.07686C18.3671 4.39249 19.6075 5.63288 19.9231 7.21964C20 7.60603 20 8.07069 20 9V9M9 20V20C8.07069 20 7.60603 20 7.21964 19.9231C5.63288 19.6075 4.39249 18.3671 4.07686 16.7804C4 16.394 4 15.9293 4 15V15M15 20V20C15.9293 20 16.394 20 16.7804 19.9231C18.3671 19.6075 19.6075 18.3671 19.9231 16.7804C20 16.394 20 15.9293 20 15V15" stroke="currentColor"></path>
                            </svg>
                            <span>Edit Image</span>
                        </button>

                        <button class="btn-feature">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-secondary">
                                <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"></path>
                                <path d="M18 14h-8"></path>
                                <path d="M15 18h-5"></path>
                                <path d="M10 6h8v4h-8V6Z"></path>
                            </svg>
                            <span>Latest News</span>
                        </button>

                        <button class="btn-feature dropdown-trigger">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-secondary">
                                <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                            <span>Personas</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-chevron-small">
                                <path d="m6 9 6 6 6-6"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Footer -->
                <div class="footer">
                    <p class="footer-text">
                        By messaging Grok, you agree to our 
                        <a href="https://x.ai/legal/terms-of-service" target="_blank" rel="noopener noreferrer" class="footer-link">Terms</a> 
                        and 
                        <a href="https://x.ai/legal/privacy-policy" target="_blank" rel="noopener noreferrer" class="footer-link">Privacy Policy</a>.
                    </p>
                </div>
            </div>
        </main>
    </div>

    </div>

    <!-- Loading Animation SVGs (hidden by default) -->
    <div class="loading-animations" style="display: none;">
        <!-- Add loading animations here if needed -->
    </div>

    <script src="script.js"></script>
</body>
</html>
    