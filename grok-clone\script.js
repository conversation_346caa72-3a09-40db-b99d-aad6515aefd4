// Theme Management
class ThemeManager {
    constructor() {
        this.theme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme(this.theme);
        this.setupListeners();
    }

    applyTheme(theme) {
        document.documentElement.className = theme;
        localStorage.setItem('theme', theme);
        this.theme = theme;
    }

    toggle() {
        const newTheme = this.theme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
    }

    setupListeners() {
        // Listen for system theme changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (!localStorage.getItem('theme')) {
                    this.applyTheme(e.matches ? 'dark' : 'light');
                }
            });
        }
    }
}

// Dropdown Manager
class DropdownManager {
    constructor() {
        this.dropdowns = new Map();
        this.init();
    }

    init() {
        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            this.dropdowns.forEach((dropdown, trigger) => {
                if (!trigger.contains(e.target) && !dropdown.contains(e.target)) {
                    this.close(trigger);
                }
            });
        });

        // Close dropdowns on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAll();
            }
        });
    }

    register(trigger, dropdown) {
        this.dropdowns.set(trigger, dropdown);
        
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggle(trigger);
        });
    }

    toggle(trigger) {
        const isOpen = trigger.getAttribute('aria-expanded') === 'true';
        if (isOpen) {
            this.close(trigger);
        } else {
            this.open(trigger);
        }
    }

    open(trigger) {
        // Close all other dropdowns
        this.closeAll();
        
        trigger.setAttribute('aria-expanded', 'true');
        const dropdown = this.dropdowns.get(trigger);
        if (dropdown) {
            dropdown.style.display = 'block';
            // Add animation class if needed
            dropdown.classList.add('dropdown-open');
        }
    }

    close(trigger) {
        trigger.setAttribute('aria-expanded', 'false');
        const dropdown = this.dropdowns.get(trigger);
        if (dropdown) {
            dropdown.style.display = 'none';
            dropdown.classList.remove('dropdown-open');
        }
    }

    closeAll() {
        this.dropdowns.forEach((dropdown, trigger) => {
            this.close(trigger);
        });
    }
}

// Search Input Manager
class SearchInputManager {
    constructor() {
        this.searchInput = document.querySelector('.search-input');
        this.searchPlaceholder = document.querySelector('.search-placeholder');
        this.init();
    }

    init() {
        if (!this.searchInput) return;

        // Auto-resize textarea
        this.searchInput.addEventListener('input', () => {
            this.autoResize();
            this.updatePlaceholder();
        });

        // Handle focus
        this.searchInput.addEventListener('focus', () => {
            this.updatePlaceholder();
        });

        // Handle blur
        this.searchInput.addEventListener('blur', () => {
            this.updatePlaceholder();
        });

        // Handle form submission
        const form = this.searchInput.closest('form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSubmit();
            });
        }

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Focus search on '/' key
            if (e.key === '/' && !this.isInputFocused()) {
                e.preventDefault();
                this.searchInput.focus();
            }
        });
    }

    autoResize() {
        this.searchInput.style.height = 'auto';
        this.searchInput.style.height = this.searchInput.scrollHeight + 'px';
    }

    updatePlaceholder() {
        if (this.searchInput.value.trim() || document.activeElement === this.searchInput) {
            this.searchPlaceholder.style.display = 'none';
        } else {
            this.searchPlaceholder.style.display = 'block';
        }
    }

    handleSubmit() {
        const query = this.searchInput.value.trim();
        if (query) {
            console.log('Search query:', query);
            // Here you would handle the search submission
            // For now, we'll just log it
        }
    }

    isInputFocused() {
        const activeElement = document.activeElement;
        return activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.contentEditable === 'true'
        );
    }
}

// Button Interactions
class ButtonInteractions {
    constructor() {
        this.init();
    }

    init() {
        // Add ripple effect to buttons
        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.createRipple(e, button);
            });
        });

        // Handle voice button animation
        const voiceButton = document.querySelector('.btn-voice');
        if (voiceButton) {
            voiceButton.addEventListener('click', () => {
                this.animateVoiceButton(voiceButton);
            });
        }

        // Handle feature buttons
        const featureButtons = document.querySelectorAll('.btn-feature');
        featureButtons.forEach(button => {
            button.addEventListener('click', () => {
                const buttonText = button.textContent.trim();
                console.log('Feature clicked:', buttonText);
                // Here you would handle feature button clicks
            });
        });

        // Handle settings button for theme toggle
        const settingsButton = document.querySelector('[aria-label="Settings"]');
        if (settingsButton) {
            settingsButton.addEventListener('click', () => {
                window.grokClone.theme.toggle();
                console.log('Theme toggled');
            });
        }

        // Handle mode toggles
        const deepSearchToggle = document.querySelector('.toggle-left');
        if (deepSearchToggle) {
            deepSearchToggle.addEventListener('click', () => {
                this.toggleDeepSearch(deepSearchToggle);
            });
        }

        const thinkToggle = document.querySelector('.btn-bordered[aria-label="Think"]');
        if (thinkToggle) {
            thinkToggle.addEventListener('click', () => {
                this.toggleThink(thinkToggle);
            });
        }
    }

    createRipple(e, button) {
        const ripple = document.createElement('span');
        ripple.classList.add('ripple');
        
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        
        button.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    animateVoiceButton(button) {
        const bars = button.querySelectorAll('.voice-bar');
        bars.forEach((bar, index) => {
            setTimeout(() => {
                bar.style.transform = 'scaleY(1.5)';
                setTimeout(() => {
                    bar.style.transform = 'scaleY(1)';
                }, 150);
            }, index * 50);
        });
    }

    toggleDeepSearch(button) {
        const isActive = button.getAttribute('aria-pressed') === 'true';
        button.setAttribute('aria-pressed', !isActive);
        console.log('DeepSearch:', !isActive ? 'enabled' : 'disabled');
    }

    toggleThink(button) {
        const isActive = button.getAttribute('aria-pressed') === 'true';
        button.setAttribute('aria-pressed', !isActive);
        console.log('Think mode:', !isActive ? 'enabled' : 'disabled');
    }
}

// Loading Animation Manager
class LoadingAnimationManager {
    constructor() {
        this.animations = [];
        this.init();
    }

    init() {
        // Create loading animation elements if needed
        this.createLoadingElements();
    }

    createLoadingElements() {
        const container = document.querySelector('.loading-animations');
        if (!container) return;

        // Create SVG loading animations similar to the original
        for (let i = 0; i < 4; i++) {
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('viewBox', '0 0 24 24');
            svg.setAttribute('id', `loading-x-anim-${i}`);
            svg.setAttribute('aria-hidden', 'true');
            svg.style.position = 'absolute';
            svg.style.width = '24px';
            svg.style.height = '24px';
            svg.style.display = 'none';
            
            // Add animation paths here
            container.appendChild(svg);
            this.animations.push(svg);
        }
    }

    show() {
        this.animations.forEach((svg, index) => {
            setTimeout(() => {
                svg.style.display = 'block';
                svg.classList.add('loading-active');
            }, index * 100);
        });
    }

    hide() {
        this.animations.forEach(svg => {
            svg.style.display = 'none';
            svg.classList.remove('loading-active');
        });
    }
}

// Accessibility Manager
class AccessibilityManager {
    constructor() {
        this.init();
    }

    init() {
        // Handle keyboard navigation
        this.setupKeyboardNavigation();
        
        // Handle focus visible
        this.setupFocusVisible();
        
        // Handle ARIA live regions
        this.setupAriaLive();
    }

    setupKeyboardNavigation() {
        // Tab trap for modals/dropdowns
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                const activeDropdown = document.querySelector('[aria-expanded="true"]');
                if (activeDropdown) {
                    // Implement tab trapping logic here
                }
            }
        });
    }

    setupFocusVisible() {
        // Add focus-visible class for keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-nav');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-nav');
        });
    }

    setupAriaLive() {
        // Create ARIA live region for announcements
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.classList.add('sr-only');
        document.body.appendChild(liveRegion);
        
        this.liveRegion = liveRegion;
    }

    announce(message) {
        if (this.liveRegion) {
            this.liveRegion.textContent = message;
            setTimeout(() => {
                this.liveRegion.textContent = '';
            }, 1000);
        }
    }
}

// Initialize everything when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Initialize managers
    const themeManager = new ThemeManager();
    const dropdownManager = new DropdownManager();
    const searchInputManager = new SearchInputManager();
    const buttonInteractions = new ButtonInteractions();
    const loadingAnimationManager = new LoadingAnimationManager();
    const accessibilityManager = new AccessibilityManager();

    // Register dropdowns
    const personasButton = document.querySelector('.dropdown-trigger');
    if (personasButton) {
        // Create dropdown menu (in real implementation, this would be in HTML)
        const dropdownMenu = document.createElement('div');
        dropdownMenu.classList.add('dropdown-menu');
        dropdownMenu.style.display = 'none';
        dropdownMenu.innerHTML = `
            <div class="dropdown-item">Default Persona</div>
            <div class="dropdown-item">Creative Persona</div>
            <div class="dropdown-item">Technical Persona</div>
        `;
        personasButton.parentElement.appendChild(dropdownMenu);
        dropdownManager.register(personasButton, dropdownMenu);
    }

    // Make managers globally accessible if needed
    window.grokClone = {
        theme: themeManager,
        dropdowns: dropdownManager,
        search: searchInputManager,
        buttons: buttonInteractions,
        loading: loadingAnimationManager,
        accessibility: accessibilityManager
    };

    // Log initialization
    console.log('Grok Clone initialized successfully!');
});

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    button {
        position: relative;
        overflow: hidden;
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s ease-out;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        margin-top: 0.5rem;
        background-color: var(--color-surface-l1);
        border: 1px solid var(--color-border-l1);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        min-width: 200px;
        z-index: 50;
    }

    .dropdown-item {
        padding: 0.75rem 1rem;
        cursor: pointer;
        transition: background-color var(--transition-fast);
    }

    .dropdown-item:hover {
        background-color: var(--color-button-ghost-hover);
    }

    .dropdown-item:first-child {
        border-radius: var(--radius-md) var(--radius-md) 0 0;
    }

    .dropdown-item:last-child {
        border-radius: 0 0 var(--radius-md) var(--radius-md);
    }

    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border-width: 0;
    }

    .keyboard-nav button:focus {
        outline: 2px solid var(--color-primary);
        outline-offset: 2px;
    }

    .voice-bar {
        transition: transform var(--transition-fast);
    }

    .loading-active {
        animation: loading-spin 1s linear infinite;
    }

    @keyframes loading-spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }
`;
document.head.appendChild(style);