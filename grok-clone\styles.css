/* CSS Reset and Base Styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* CSS Variables for Theme System */
:root {
    /* Light Theme Colors */
    --color-background: #ffffff;
    --color-surface-base: #ffffff;
    --color-surface-l1: #f9fafb;
    --color-surface-l2: #f3f4f6;
    --color-fg-primary: #111827;
    --color-fg-secondary: #6b7280;
    --color-fg-tertiary: #9ca3af;
    --color-fg-invert: #ffffff;
    --color-border-l1: #e5e7eb;
    --color-border-l2: #d1d5db;
    --color-button-primary: #111827;
    --color-button-primary-hover: #1f2937;
    --color-button-ghost-hover: rgba(0, 0, 0, 0.05);
    --color-button-secondary-hover: rgba(0, 0, 0, 0.05);
    --color-toggle-border: #e5e7eb;
    --color-highlight: rgba(59, 130, 246, 0.1);
    --color-primary: #2563eb;
    --color-secondary: #6b7280;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 100ms ease-in-out;
    --transition-base: 200ms ease-in-out;
    --transition-slow: 300ms ease-in-out;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Dark Theme */
html.dark {
    --color-background: #0f0f0f;
    --color-surface-base: #0f0f0f;
    --color-surface-l1: #1a1a1a;
    --color-surface-l2: #262626;
    --color-fg-primary: #ffffff;
    --color-fg-secondary: #a3a3a3;
    --color-fg-tertiary: #737373;
    --color-fg-invert: #0f0f0f;
    --color-border-l1: #262626;
    --color-border-l2: #404040;
    --color-button-primary: #ffffff;
    --color-button-primary-hover: #e5e5e5;
    --color-button-ghost-hover: rgba(255, 255, 255, 0.1);
    --color-button-secondary-hover: rgba(255, 255, 255, 0.1);
    --color-toggle-border: #404040;
    --color-highlight: rgba(59, 130, 246, 0.2);
    --color-primary: #3b82f6;
    --color-secondary: #a3a3a3;
}

/* Base Styles */
html {
    font-size: 16px;
    color-scheme: light;
}

html.dark {
    color-scheme: dark;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--color-fg-primary);
    background-color: var(--color-background);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    letter-spacing: -0.1px;
}

/* Utility Classes */
.antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.desktop-only {
    display: none;
}

@media (min-width: 640px) {
    .desktop-only {
        display: flex;
    }
}

/* Layout Components */
.app-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    width: 100%;
}

/* Navigation Bar */
.navbar {
    position: relative;
    width: 100%;
    z-index: 25;
    flex-shrink: 0;
}

.nav-container {
    height: 4rem;
    position: absolute;
    top: 0;
    z-index: 10;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    background: linear-gradient(to bottom, var(--color-background) 0%, var(--color-background) 80%, transparent 100%);
    padding: 0 1rem;
}

@media (min-width: 1280px) {
    .nav-container {
        height: 0;
        top: 2rem;
        background: transparent;
    }
}

.nav-logo {
    position: absolute;
    left: 0.25rem;
}

.logo-link {
    display: inline-block;
    border-radius: var(--radius-md);
    margin-left: 0.5rem;
    margin-right: 0.125rem;
    text-decoration: none;
    outline: none;
}

.logo-link:focus-visible {
    box-shadow: 0 0 0 1px var(--color-border-l2);
}

.logo-svg {
    opacity: 0.9;
    fill: currentColor;
    max-width: 100%;
    transition: opacity var(--transition-fast);
}

.logo-svg:hover {
    opacity: 1;
}

.nav-actions {
    position: absolute;
    right: 0.75rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
}

/* Button Styles */
.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--radius-full);
    background-color: transparent;
    border: 1px solid transparent;
    color: var(--color-fg-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    outline: none;
}

.btn-icon:hover {
    background-color: var(--color-button-ghost-hover);
}

.btn-icon:focus-visible {
    box-shadow: 0 0 0 1px var(--color-border-l2);
}

.btn-icon:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    height: 2rem;
    background-color: var(--color-button-primary);
    color: var(--color-background);
    border: none;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    outline: none;
    white-space: nowrap;
}

.btn-primary:hover {
    background-color: var(--color-button-primary-hover);
}

.btn-primary:focus-visible {
    box-shadow: 0 0 0 1px var(--color-border-l2);
}

.btn-secondary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    height: 2rem;
    background-color: transparent;
    color: var(--color-primary);
    border: 1px solid var(--color-toggle-border);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    outline: none;
    white-space: nowrap;
}

.btn-secondary:hover {
    background-color: var(--color-button-secondary-hover);
    border-color: var(--color-border-l2);
}

.btn-secondary:focus-visible {
    box-shadow: 0 0 0 1px var(--color-border-l2);
}

/* Icon Styles */
.icon {
    stroke-width: 2;
    flex-shrink: 0;
}

.icon-secondary {
    stroke-width: 2;
    color: var(--color-fg-secondary);
    transition: color var(--transition-fast);
}

.icon-chevron {
    stroke-width: 2;
    color: var(--color-secondary);
    transition: transform var(--transition-base);
}

.icon-chevron-small {
    color: var(--color-secondary);
    transition: transform var(--transition-base);
}

/* Main Content */
.main-content {
    height: 100vh;
    flex-grow: 1;
    flex-shrink: 1;
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow-y: auto;
}

.content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 0.5rem;
    margin: 0 auto;
    justify-content: center;
    gap: 2.25rem;
    margin-top: 4rem;
}

@media (min-width: 640px) {
    .content-wrapper {
        padding: 1rem;
        margin-top: 0;
    }
}

/* Logo Container */
.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 0.5rem;
    gap: 1.5rem;
    max-width: 1200px;
}

@media (min-width: 640px) {
    .logo-container {
        padding: 3rem 1rem 0;
        gap: 1rem;
    }
}

@media (min-width: 1280px) {
    .logo-container {
        width: 80%;
    }
}

/* Search Container */
.search-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    max-width: 1200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: 0.25rem;
}

@media (min-width: 640px) {
    .search-container {
        position: relative;
        gap: 1.25rem;
        bottom: auto;
        left: auto;
        right: auto;
        max-width: 100%;
    }
}

.search-form {
    width: 100%;
    font-size: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 10;
    margin-top: 0.5rem;
}

.search-box {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    justify-content: center;
    width: 100%;
    position: relative;
}

@media (min-width: 1280px) {
    .search-box {
        width: 80%;
    }
}

/* Search Input */
.search-input-wrapper {
    position: relative;
    z-index: 10;
    background-color: var(--color-surface-l2);
    width: 100%;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    max-width: 1200px;
    border: 1px solid var(--color-border-l1);
    border-radius: 1.5rem;
    padding-bottom: 3rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    transition: all var(--transition-fast);
}

@media (min-width: 480px) {
    .search-input-wrapper {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
}

.search-input-wrapper:hover {
    border-color: var(--color-border-l2);
}

.search-input-wrapper:focus-within {
    border-color: var(--color-border-l2);
}

.search-placeholder {
    position: absolute;
    padding: 1.25rem 0.5rem;
    color: var(--color-fg-secondary);
    pointer-events: none;
    user-select: none;
}

@media (min-width: 480px) {
    .search-placeholder {
        padding: 1.25rem 0.75rem;
    }
}

.search-input {
    width: 100%;
    padding: 1.25rem 0.5rem;
    background-color: transparent;
    border: none;
    outline: none;
    color: var(--color-fg-primary);
    resize: none;
    min-height: 3.5rem;
    margin-bottom: 1.25rem;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.5;
}

@media (min-width: 480px) {
    .search-input {
        padding: 1.25rem 0.75rem;
    }
}

.search-input:focus + .search-placeholder,
.search-input:not(:placeholder-shown) + .search-placeholder {
    display: none;
}

/* Search Actions */
.search-actions {
    display: flex;
    gap: 0.375rem;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    border: 2px solid transparent;
    padding: 0.5rem;
    max-width: 100%;
}

@media (min-width: 480px) {
    .search-actions {
        padding: 0.5rem;
    }
}

.btn-icon-bordered {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--radius-full);
    background-color: transparent;
    border: 1px solid var(--color-border-l2);
    color: var(--color-fg-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    outline: none;
}

.btn-icon-bordered:hover:not(:disabled) {
    background-color: var(--color-button-ghost-hover);
}

.btn-icon-bordered:hover:not(:disabled) .icon-secondary {
    color: var(--color-fg-primary);
}

.btn-icon-bordered:focus-visible {
    box-shadow: 0 0 0 1px var(--color-border-l2);
}

.btn-icon-bordered:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Mode Toggles */
.mode-toggles {
    display: flex;
    flex-grow: 1;
    gap: 0.375rem;
    max-width: 100%;
}

.toggle-group {
    display: flex;
    border: 1px solid var(--color-border-l2);
    border-radius: var(--radius-full);
    align-items: center;
    max-height: 2.5rem;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.toggle-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    white-space: nowrap;
    font-weight: 500;
    cursor: pointer;
    outline: none;
    height: 2.5rem;
    padding: 0.375rem 0.875rem;
    font-size: 0.875rem;
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
    background-color: transparent;
    border: none;
    color: var(--color-fg-primary);
}

.toggle-btn:hover {
    background-color: var(--color-button-ghost-hover);
}

.toggle-btn:focus-visible {
    background-color: var(--color-button-ghost-hover);
}

.toggle-left {
    border-radius: var(--radius-full) 0 0 var(--radius-full);
    padding-right: 0.75rem;
}

.toggle-right {
    border-radius: 0 var(--radius-full) var(--radius-full) 0;
    padding-left: 0.5rem;
    padding-right: 0.75rem;
}

.toggle-divider {
    height: 1rem;
    width: 1px;
    background-color: var(--color-toggle-border);
}

.btn-bordered {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    white-space: nowrap;
    font-weight: 500;
    cursor: pointer;
    outline: none;
    border: 1px solid var(--color-border-l2);
    color: var(--color-fg-primary);
    background-color: transparent;
    height: 2.5rem;
    padding: 0.375rem 0.875rem;
    font-size: 0.875rem;
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
}

.btn-bordered:hover {
    background-color: var(--color-button-ghost-hover);
}

.btn-bordered:hover .icon-secondary {
    color: var(--color-fg-primary);
}

.btn-bordered:focus-visible {
    box-shadow: 0 0 0 1px var(--color-border-l2);
}

/* Model Selector */
.btn-model {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    white-space: nowrap;
    font-weight: 500;
    cursor: pointer;
    outline: none;
    color: var(--color-fg-primary);
    background-color: transparent;
    height: 2.5rem;
    padding: 0.375rem 0.875rem;
    font-size: 0.875rem;
    border-radius: var(--radius-full);
    border: 1px solid var(--color-border-l2);
    transition: all var(--transition-fast);
}

@media (min-width: 640px) {
    .btn-model {
        border: none;
    }
}

.btn-model:hover {
    background-color: var(--color-button-ghost-hover);
}

.btn-model:focus-visible {
    box-shadow: 0 0 0 1px var(--color-border-l2);
}

.model-name {
    font-size: 0.75rem;
    color: var(--color-primary);
}

@media (min-width: 400px) {
    .model-name {
        font-size: 0.875rem;
    }
}

/* Voice Button */
.btn-voice {
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: var(--radius-full);
    outline: none;
    cursor: pointer;
    background-color: transparent;
    border: none;
    margin-left: auto;
}

.btn-voice:focus-visible {
    box-shadow: 0 0 0 1px var(--color-border-l2);
}

.voice-bars {
    height: 2.5rem;
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.125rem;
    border-radius: var(--radius-full);
    border: 1px solid transparent;
    transition: all var(--transition-fast);
    background-color: var(--color-button-primary);
    color: var(--color-fg-invert);
    position: relative;
}

.voice-bars::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: var(--radius-full);
    background-color: var(--color-button-primary);
    transition: all var(--transition-fast);
    clip-path: circle(50% at 50% 50%);
}

.voice-bar {
    width: 0.125rem;
    position: relative;
    z-index: 10;
    border-radius: var(--radius-full);
    background-color: var(--color-fg-invert);
}

/* Feature Buttons */
.feature-buttons {
    width: 100%;
    display: flex;
    justify-content: center;
    z-index: 10;
    min-height: 2.5rem;
    max-width: 1200px;
    overflow: visible;
}

@media (min-width: 640px) {
    .feature-buttons {
        display: block;
    }
}

@media (min-width: 1280px) {
    .feature-buttons {
        width: 80%;
    }
}

.feature-buttons {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
}

.btn-feature {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    white-space: nowrap;
    font-weight: 500;
    cursor: pointer;
    outline: none;
    border: 1px solid var(--color-border-l2);
    color: var(--color-fg-primary);
    background-color: transparent;
    height: 2.5rem;
    padding: 0.375rem 0.875rem;
    font-size: 0.875rem;
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
}

.btn-feature:hover {
    background-color: var(--color-button-ghost-hover);
}

.btn-feature:hover .icon-secondary {
    color: var(--color-fg-primary);
}

.btn-feature:focus-visible {
    box-shadow: 0 0 0 1px var(--color-border-l2);
}

.dropdown-trigger {
    position: relative;
}

.dropdown-trigger[aria-expanded="true"] .icon-chevron-small {
    transform: rotate(180deg);
}

/* Footer */
.footer {
    font-size: 0.6875rem;
    color: var(--color-secondary);
    white-space: nowrap;
}

@media (min-width: 640px) {
    .footer {
        font-size: 0.75rem;
    }
}

.footer-text {
    text-align: center;
}

.footer-link {
    color: var(--color-primary);
    text-decoration: none;
}

.footer-link:hover {
    text-decoration: underline;
}

/* Loading Animations */
.loading-animations {
    position: fixed;
    top: 0;
    left: 0;
    pointer-events: none;
}

/* Responsive Utilities */
@media (max-width: 639px) {
    .search-container {
        padding: 0 0.5rem 1rem;
    }
    
    .feature-buttons {
        display: none;
    }
}

/* Focus Visible Polyfill */
.js-focus-visible :focus:not(.focus-visible) {
    outline: none;
}

/* Selection Styles */
::selection {
    background-color: var(--color-highlight);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: var(--color-border-l2);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-fg-tertiary);
}

/* Additional hover states */
.btn-icon:hover .icon,
.btn-icon-bordered:hover .icon {
    color: var(--color-fg-primary);
}

.btn-feature:hover {
    transform: translateY(-1px);
}

/* Active states for toggle buttons */
.toggle-btn[aria-pressed="true"] {
    background-color: var(--color-surface-l2);
}

.btn-bordered[aria-pressed="true"] {
    background-color: var(--color-surface-l2);
    border-color: var(--color-primary);
}

.btn-bordered[aria-pressed="true"] .icon-secondary {
    color: var(--color-primary);
}

/* Focus states */
.search-input:focus {
    outline: none;
}

.search-input-wrapper:focus-within {
    box-shadow: 0 0 0 2px var(--color-primary);
}

/* Responsive adjustments for search box */
@media (max-width: 480px) {
    .mode-toggles {
        flex-wrap: wrap;
    }
    
    .btn-model {
        display: none;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .search-container,
    .footer {
        display: none;
    }
    
    .main-content {
        height: auto;
    }
}