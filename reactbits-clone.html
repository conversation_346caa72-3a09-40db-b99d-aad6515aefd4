<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Bits - Animated UI Components For React</title>
    <meta name="description" content="An open source collection of high quality, animated, interactive & fully customizable React components for building stunning, memorable user interfaces.">
    <meta name="theme-color" content="#060010">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:opsz,wght@12..96,200..800&family=Figtree:ital,wght@0,300..900;1,300..900&display=swap" rel="stylesheet">

    <style>
        :root {
            --bg-primary: #060010;
            --bg-secondary: #0a0a0a;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --accent-purple: #8400ff;
            --accent-blue: #4c1d95;
            --border-color: rgba(255, 255, 255, 0.1);
            --card-bg: rgba(255, 255, 255, 0.05);
            --gradient-purple: linear-gradient(135deg, #8400ff, #4c1d95);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DM Sans', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(6, 0, 16, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo svg {
            height: 23px;
            cursor: pointer;
        }

        .nav-cta-group {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .landing-nav-items {
            display: flex;
            gap: 2rem;
        }

        .nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover,
        .nav-link.active-link {
            color: var(--text-primary);
        }

        .cta-button {
            background: var(--gradient-purple);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: transform 0.2s ease;
        }

        .cta-button:hover {
            transform: translateY(-2px);
        }

        /* Main Content */
        .landing-wrapper {
            position: relative;
            min-height: 100vh;
            background: var(--bg-primary);
        }

        .background-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
        }

        .landing-content {
            position: relative;
            z-index: 1;
            padding-top: 120px;
            max-width: 1200px;
            margin: 0 auto;
            padding-left: 2rem;
            padding-right: 2rem;
        }

        /* Hero Section */
        .hero-main-content {
            text-align: center;
            margin-bottom: 4rem;
        }

        .landing-title {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #ffffff, #a0a0a0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.1;
        }

        .hero-split {
            display: inline-block;
            animation: fadeInUp 1s ease-out;
        }

        .landing-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .landing-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--gradient-purple);
            color: white;
            text-decoration: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(132, 0, 255, 0.3);
        }

        .landing-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(132, 0, 255, 0.4);
        }

        .button-arrow-circle {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Hero Cards */
        .hero-cards-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 6rem;
        }

        .hero-cards-row {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 1rem;
        }

        .hero-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 2rem;
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
            min-height: 200px;
        }

        .hero-card-1 {
            min-height: 150px;
        }

        .hero-card-2 {
            background: #000;
        }

        .hero-card-3 {
            background: var(--card-bg);
        }

        /* Features Section */
        .features-section {
            padding: 6rem 0;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .features-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .features-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .features-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
        }

        .bento-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            backdrop-filter: blur(20px);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card h2 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: var(--gradient-purple);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: var(--text-secondary);
        }

        .messages-gif,
        .components-gif,
        .switch-gif {
            width: 100%;
            max-width: 200px;
            height: auto;
            margin-bottom: 1rem;
            border-radius: 8px;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .hero-card {
            animation: float 6s ease-in-out infinite;
        }

        .hero-card:nth-child(2) {
            animation-delay: -2s;
        }

        .hero-card:nth-child(3) {
            animation-delay: -4s;
        }

        /* Testimonials Section */
        .testimonials-section {
            padding: 6rem 0;
            overflow: hidden;
        }

        .testimonials-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .testimonials-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .testimonials-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .testimonials-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
        }

        .testimonials-marquee-container {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .testimonial-row {
            display: flex;
            width: 100%;
        }

        .testimonial-marquee {
            display: flex;
            gap: 2rem;
            animation: scroll 40s linear infinite;
            white-space: nowrap;
        }

        .testimonial-marquee-left {
            animation-direction: normal;
        }

        .testimonial-marquee-right {
            animation-direction: reverse;
        }

        .testimonial-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 1.5rem;
            min-width: 350px;
            backdrop-filter: blur(20px);
        }

        .testimonial-text {
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .testimonial-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }

        .testimonial-handle {
            color: var(--text-secondary);
            font-weight: 500;
        }

        @keyframes scroll {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-100%);
            }
        }

        /* Start Building Section */
        .start-building-section {
            padding: 6rem 0;
        }

        .start-building-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .start-building-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 24px;
            padding: 4rem 2rem;
            text-align: center;
            backdrop-filter: blur(20px);
        }

        .start-building-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .start-building-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .start-building-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--gradient-purple);
            color: white;
            text-decoration: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(132, 0, 255, 0.3);
        }

        .start-building-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(132, 0, 255, 0.4);
        }

        /* Footer */
        .landing-footer {
            padding: 4rem 0 2rem;
            border-top: 1px solid var(--border-color);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 2rem;
        }

        .footer-left {
            flex: 1;
        }

        .footer-logo {
            height: 32px;
            margin-bottom: 1rem;
        }

        .footer-description {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .footer-heart {
            color: #ff6b6b;
        }

        .footer-creator-link {
            color: var(--accent-purple);
            text-decoration: none;
        }

        .footer-copyright {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .footer-links {
            display: flex;
            gap: 2rem;
        }

        .footer-link {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-link:hover {
            color: var(--text-primary);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-container {
                padding: 1rem;
            }

            .nav-cta-group {
                gap: 1rem;
            }

            .landing-nav-items {
                display: none;
            }

            .hero-cards-row {
                grid-template-columns: 1fr;
            }

            .landing-content {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .bento-grid {
                grid-template-columns: 1fr;
            }

            .testimonial-card {
                min-width: 280px;
            }

            .footer-content {
                flex-direction: column;
                text-align: center;
            }

            .footer-links {
                justify-content: center;
                flex-wrap: wrap;
            }

            .start-building-card {
                padding: 3rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a class="logo" href="#">
                <svg width="115" height="23" viewBox="0 0 115 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path class="atom" d="M15.6317 0.571987C16.3759 0.228574 17.2363 0.0550841 18.033 0.38644L18.1922 0.459682L18.1942 0.460659L18.41 0.587612C18.8912 0.905222 19.2097 1.37902 19.407 1.8923C19.602 2.39947 19.6967 2.98342 19.7195 3.60226L19.7244 3.86886L19.7293 4.82101L17.825 4.83078L17.8201 3.87863L17.8045 3.47042C17.7767 3.09113 17.7136 2.79437 17.6297 2.57589C17.5217 2.29489 17.4024 2.19635 17.3289 2.15792L17.284 2.13937C17.1618 2.09871 16.9051 2.08256 16.4305 2.30148C15.8996 2.5465 15.2436 3.02482 14.5086 3.74874C13.7215 4.524 12.8888 5.53694 12.0692 6.73507C12.2359 6.73273 12.4038 6.73019 12.5721 6.73019C15.8011 6.73022 18.765 7.16566 20.9539 7.89523C22.0431 8.25833 22.9938 8.71183 23.6912 9.26144C24.3778 9.80264 24.9519 10.5493 24.952 11.4919C24.9519 12.3043 24.5214 12.9748 23.9715 13.4831C23.4218 13.9912 22.6749 14.416 21.824 14.7673L20.9432 15.1306L20.2166 13.3698L21.0975 13.0066C21.833 12.7029 22.3554 12.3832 22.6785 12.0847C23.0011 11.7865 23.0476 11.5861 23.0477 11.4919C23.0476 11.3827 22.9814 11.1272 22.5125 10.7575C22.0535 10.3958 21.3298 10.028 20.3514 9.70187C18.4042 9.05287 15.6534 8.6355 12.5721 8.63546C11.9839 8.63546 11.4073 8.65026 10.8465 8.67941C10.5662 9.16529 10.292 9.6716 10.0252 10.195C9.8036 10.6298 9.59539 11.0625 9.40021 11.49C9.59576 11.9183 9.80411 12.3521 10.0262 12.7878H10.0272L10.2918 13.2976C11.6276 15.814 13.1379 17.8859 14.5086 19.236C15.2432 19.9595 15.8989 20.4376 16.4295 20.6823C16.9715 20.9322 17.23 20.8755 17.3279 20.8259L17.4061 20.7712C17.4946 20.6921 17.6094 20.5285 17.699 20.1902C17.8183 19.7396 17.8576 19.0916 17.7781 18.2556C17.6198 16.5909 17.0111 14.39 15.9705 12.0017L16.8436 11.6218L17.7166 11.2409C18.809 13.7484 19.4914 16.1499 19.6746 18.0749C19.7658 19.0336 19.7391 19.9287 19.5408 20.6775C19.343 21.4244 18.9388 22.1417 18.1932 22.5222L18.1922 22.5232C17.3525 22.9505 16.4266 22.7778 15.6326 22.4118C14.8262 22.04 13.9897 21.3992 13.1717 20.5935C11.5669 19.0128 9.8785 16.6456 8.43536 13.8562C7.94269 15.2332 7.60957 16.5119 7.44415 17.611C7.29073 18.6308 7.29006 19.4422 7.40411 20.0153C7.52086 20.6015 7.71887 20.7763 7.81622 20.8259H7.8172L7.88947 20.8523C8.09827 20.9026 8.58562 20.8602 9.46173 20.239L10.2381 19.6882L11.3397 21.2419L10.5633 21.7927L10.3582 21.9323C9.31984 22.6214 8.06557 23.0899 6.95197 22.5222V22.5232C6.11192 22.0955 5.70676 21.2449 5.53595 20.3874C5.36259 19.5163 5.38948 18.4625 5.56036 17.3269C5.81724 15.6203 6.41817 13.6025 7.31818 11.49C6.99301 10.7266 6.7076 9.97508 6.46271 9.24679C5.85761 9.38078 5.298 9.53349 4.79279 9.70187C3.81426 10.028 3.09071 10.3958 2.63165 10.7575C2.16243 11.1274 2.09658 11.3827 2.0965 11.4919C2.09671 11.6513 2.25906 12.068 3.17462 12.5876L3.36798 12.6921L4.21271 13.1335L3.3299 14.822L2.48615 14.3806L2.2674 14.2614C1.17979 13.6487 0.192367 12.742 0.1922 11.4919C0.192288 10.5492 0.766185 9.80266 1.45294 9.26144C2.15046 8.7118 3.10094 8.25834 4.19025 7.89523C4.72343 7.71753 5.30301 7.55645 5.92072 7.41574C5.76557 6.80139 5.64509 6.21178 5.56134 5.65597C5.39035 4.52081 5.36283 3.46829 5.53595 2.59738C5.70654 1.7396 6.11162 0.888515 6.95197 0.460659V0.461635C7.83252 0.0128116 8.80234 0.2186 9.65021 0.63937L10.0057 0.831752L10.8279 1.31222L9.86603 2.95675L9.04376 2.47628L8.78009 2.33371C8.20699 2.05202 7.91997 2.1051 7.81622 2.15792V2.1589C7.71887 2.20847 7.52073 2.38226 7.40411 2.96847C7.29019 3.54153 7.29149 4.35303 7.44513 5.37277C7.52452 5.89955 7.64219 6.46785 7.79767 7.06808C8.41596 6.97652 9.06014 6.90151 9.72443 6.8464C10.8365 5.04912 12.0257 3.5201 13.1717 2.39132C13.9899 1.58545 14.8252 0.944189 15.6317 0.571987ZM17.7166 11.2409L15.9705 12.0017L15.5906 11.1286L17.3367 10.3679L17.7166 11.2409ZM8.4422 7.94406C7.87898 7.94428 7.42287 8.40036 7.42267 8.96359C7.42267 9.52698 7.87886 9.98388 8.4422 9.9841C9.00573 9.9841 9.46271 9.52712 9.46271 8.96359C9.46251 8.40023 9.00561 7.94406 8.4422 7.94406Z" fill="white"></path>
                    <path d="M33.5883 17.3539V4.68017H38.2209C39.3025 4.68017 40.1886 4.84916 40.8793 5.18712C41.5699 5.52509 42.0781 5.97772 42.4039 6.54502C42.7427 7.10025 42.9121 7.73394 42.9121 8.44608C42.9121 9.12202 42.7427 9.74967 42.4039 10.329C42.0781 10.8963 41.5634 11.355 40.8597 11.705C40.156 12.043 39.2569 12.212 38.1622 12.212H35.5429V17.3539H33.5883ZM40.7815 17.3539L37.8299 11.7232H39.9801L43.049 17.3539H40.7815ZM35.5429 10.836H38.0841C39.0484 10.836 39.7586 10.6187 40.2147 10.1842C40.6708 9.7376 40.8988 9.1703 40.8988 8.4823C40.8988 7.78222 40.6773 7.22699 40.2342 6.8166C39.7911 6.40622 39.0744 6.20102 38.0841 6.20102H35.5429V10.836Z" fill="white"></path>
                    <path d="M48.2323 17.5712C47.294 17.5712 46.46 17.372 45.7303 16.9737C45.0005 16.5754 44.4271 16.0202 44.0101 15.308C43.6062 14.5959 43.4042 13.7691 43.4042 12.8276C43.4042 11.862 43.6062 11.0231 44.0101 10.3109C44.4271 9.58672 45.0005 9.02546 45.7303 8.62714C46.46 8.21675 47.307 8.01156 48.2714 8.01156C49.2357 8.01156 50.0632 8.21072 50.7538 8.60903C51.4445 9.00735 51.9788 9.53844 52.3567 10.2023C52.7346 10.8541 52.9235 11.5783 52.9235 12.3749C52.9235 12.4956 52.917 12.6284 52.904 12.7733C52.904 12.906 52.8975 13.0569 52.8844 13.2259H44.8311V11.9404H50.9688C50.9297 11.18 50.6561 10.5886 50.1479 10.1661C49.6396 9.73156 49.0076 9.5143 48.2518 9.5143C47.7175 9.5143 47.2289 9.62897 46.7858 9.8583C46.3427 10.0756 45.9844 10.4015 45.7107 10.836C45.4501 11.2584 45.3198 11.7956 45.3198 12.4474V12.9543C45.3198 13.6302 45.4501 14.2036 45.7107 14.6743C45.9844 15.133 46.3427 15.483 46.7858 15.7244C47.2289 15.9538 47.711 16.0684 48.2323 16.0684C48.8578 16.0684 49.3725 15.9417 49.7765 15.6882C50.1804 15.4347 50.4802 15.0907 50.6756 14.6562H52.6303C52.4609 15.2114 52.1742 15.7124 51.7703 16.159C51.3663 16.5935 50.8646 16.9375 50.2651 17.191C49.6787 17.4444 49.0011 17.5712 48.2323 17.5712Z" fill="white"></path>
                    <path d="M57.3822 17.5712C56.5743 17.5712 55.9032 17.4444 55.3689 17.191C54.8346 16.9375 54.4371 16.5995 54.1765 16.1771C53.9159 15.7425 53.7856 15.2778 53.7856 14.783C53.7856 14.1794 53.955 13.6665 54.2938 13.244C54.6326 12.8095 55.1148 12.4775 55.7403 12.2482C56.3658 12.0189 57.1151 11.9042 57.9882 11.9042H60.5488C60.5488 11.3731 60.4641 10.9326 60.2947 10.5825C60.1253 10.2325 59.8712 9.97297 59.5324 9.80399C59.2066 9.62293 58.7896 9.53241 58.2814 9.53241C57.695 9.53241 57.1933 9.66518 56.7763 9.93072C56.3593 10.1842 56.0986 10.5644 55.9944 11.0714H54.0397C54.1179 10.4316 54.3524 9.88848 54.7434 9.44188C55.1473 8.98321 55.6621 8.63317 56.2876 8.39177C56.9131 8.13829 57.5777 8.01156 58.2814 8.01156C59.2066 8.01156 59.982 8.16243 60.6075 8.46419C61.233 8.76595 61.7021 9.19444 62.0148 9.74967C62.3406 10.2928 62.5035 10.9446 62.5035 11.705V17.3539H60.8029L60.6465 15.815C60.5032 16.0564 60.3338 16.2857 60.1383 16.503C59.9429 16.7202 59.7083 16.9073 59.4346 17.0642C59.174 17.2211 58.8678 17.3418 58.5159 17.4263C58.1771 17.5229 57.7992 17.5712 57.3822 17.5712ZM57.7536 16.1046C58.1706 16.1046 58.5485 16.0262 58.8873 15.8693C59.2261 15.7124 59.5128 15.5011 59.7474 15.2356C59.995 14.958 60.1774 14.6502 60.2947 14.3122C60.425 13.9622 60.4967 13.6061 60.5097 13.244V13.1897H58.1836C57.6233 13.1897 57.1672 13.2561 56.8154 13.3888C56.4765 13.5095 56.2289 13.6785 56.0726 13.8958C55.9162 14.1131 55.838 14.3665 55.838 14.6562C55.838 14.958 55.9097 15.2175 56.053 15.4347C56.2094 15.6399 56.4309 15.8029 56.7176 15.9236C57.0043 16.0443 57.3496 16.1046 57.7536 16.1046Z" fill="white"></path>
                    <path d="M68.5756 17.5712C67.6243 17.5712 66.7773 17.372 66.0345 16.9737C65.2917 16.5633 64.7053 16.0021 64.2753 15.2899C63.8583 14.5778 63.6498 13.751 63.6498 12.8095C63.6498 11.8559 63.8583 11.0231 64.2753 10.3109C64.7053 9.58672 65.2917 9.02546 66.0345 8.62714C66.7773 8.21675 67.6243 8.01156 68.5756 8.01156C69.7745 8.01156 70.7779 8.30124 71.5858 8.88061C72.3937 9.45998 72.9085 10.2445 73.13 11.2343H71.0971C70.9668 10.7032 70.6671 10.2928 70.198 10.0031C69.7419 9.71346 69.1946 9.56862 68.556 9.56862C68.0348 9.56862 67.5526 9.69535 67.1096 9.94883C66.6665 10.1902 66.3081 10.5523 66.0345 11.0351C65.7739 11.5059 65.6435 12.0913 65.6435 12.7914C65.6435 13.3104 65.7217 13.7751 65.8781 14.1855C66.0345 14.5838 66.243 14.9218 66.5036 15.1994C66.7773 15.477 67.09 15.6882 67.4419 15.8331C67.7937 15.9658 68.1651 16.0322 68.556 16.0322C68.9861 16.0322 69.3705 15.9719 69.7093 15.8512C70.0611 15.7184 70.3543 15.5253 70.5889 15.2718C70.8365 15.0183 71.0059 14.7166 71.0971 14.3665H73.13C72.9085 15.3322 72.3937 16.1107 71.5858 16.7021C70.7779 17.2815 69.7745 17.5712 68.5756 17.5712Z" fill="white"></path>
                    <path d="M78.2956 17.3539C77.6701 17.3539 77.1293 17.2634 76.6732 17.0823C76.2171 16.9013 75.8653 16.5995 75.6177 16.1771C75.3701 15.7546 75.2463 15.1813 75.2463 14.4571V9.76777H73.5457V8.22882H75.2463L75.4808 5.94755H77.201V8.22882H79.9962V9.76777H77.201V14.4752C77.201 14.9942 77.3182 15.3503 77.5528 15.5434C77.7874 15.7244 78.1913 15.815 78.7647 15.815H79.8984V17.3539H78.2956Z" fill="white"></path>
                    <path d="M85.296 17.3539V4.68017H90.3586C91.3099 4.68017 92.1048 4.82501 92.7434 5.1147C93.3819 5.39232 93.8575 5.77856 94.1703 6.27344C94.4961 6.75625 94.659 7.30545 94.659 7.92103C94.659 8.56075 94.5091 9.09788 94.2094 9.53241C93.9097 9.96693 93.5122 10.3049 93.017 10.5463C92.5349 10.7756 92.0136 10.9084 91.4533 10.9446L91.7269 10.7636C92.3264 10.7756 92.8737 10.9265 93.3689 11.2162C93.8641 11.4938 94.255 11.868 94.5417 12.3387C94.8284 12.8095 94.9717 13.3285 94.9717 13.8958C94.9717 14.5476 94.8023 15.139 94.4635 15.6701C94.1247 16.1891 93.623 16.5995 92.9584 16.9013C92.2938 17.203 91.4793 17.3539 90.515 17.3539H85.296ZM87.2507 15.8512H90.2609C91.121 15.8512 91.7856 15.6701 92.2547 15.308C92.7369 14.9338 92.9779 14.4088 92.9779 13.7328C92.9779 13.069 92.7303 12.5379 92.2351 12.1396C91.753 11.7413 91.0819 11.5421 90.2218 11.5421H87.2507V15.8512ZM87.2507 10.148H90.1436C90.9646 10.148 91.5901 9.97297 92.0201 9.62293C92.4502 9.26083 92.6652 8.76595 92.6652 8.13829C92.6652 7.53478 92.4502 7.05801 92.0201 6.70797C91.5901 6.34586 90.9451 6.16481 90.085 6.16481H87.2507V10.148Z" fill="white"></path>
                    <path d="M96.3646 17.3539V8.22882H98.3193V17.3539H96.3646ZM97.3615 6.50881C96.9836 6.50881 96.6708 6.40018 96.4232 6.18292C96.1887 5.96565 96.0714 5.68804 96.0714 5.35007C96.0714 5.02417 96.1887 4.75863 96.4232 4.55344C96.6708 4.33617 96.9836 4.22754 97.3615 4.22754C97.7264 4.22754 98.0326 4.33617 98.2802 4.55344C98.5278 4.75863 98.6516 5.02417 98.6516 5.35007C98.6516 5.68804 98.5278 5.96565 98.2802 6.18292C98.0326 6.40018 97.7264 6.50881 97.3615 6.50881Z" fill="white"></path>
                    <path d="M104.203 17.3539C103.577 17.3539 103.037 17.2634 102.581 17.0823C102.124 16.9013 101.773 16.5995 101.525 16.1771C101.277 15.7546 101.154 15.1813 101.154 14.4571V9.76777H99.453V8.22882H101.154L101.388 5.94755H103.108V8.22882H105.903V9.76777H103.108V14.4752C103.108 14.9942 103.226 15.3503 103.46 15.5434C103.695 15.7244 104.099 15.815 104.672 15.815H105.806V17.3539H104.203Z" fill="white"></path>
                    <path d="M110.893 17.5712C110.059 17.5712 109.33 17.4444 108.704 17.191C108.079 16.9375 107.583 16.5814 107.218 16.1228C106.854 15.6641 106.632 15.127 106.554 14.5114H108.548C108.613 14.8011 108.737 15.0666 108.919 15.308C109.115 15.5494 109.375 15.7425 109.701 15.8874C110.04 16.0322 110.437 16.1046 110.893 16.1046C111.323 16.1046 111.675 16.0503 111.949 15.9417C112.236 15.821 112.444 15.6641 112.574 15.471C112.705 15.2658 112.77 15.0485 112.77 14.8192C112.77 14.4812 112.679 14.2277 112.496 14.0587C112.327 13.8777 112.066 13.7389 111.714 13.6423C111.375 13.5337 110.965 13.4371 110.483 13.3526C110.027 13.2802 109.584 13.1837 109.154 13.0629C108.737 12.9302 108.359 12.7672 108.02 12.5741C107.694 12.381 107.433 12.1396 107.238 11.8499C107.043 11.5481 106.945 11.18 106.945 10.7455C106.945 10.2264 107.095 9.76174 107.394 9.35135C107.694 8.92889 108.118 8.603 108.665 8.37366C109.225 8.13226 109.883 8.01156 110.639 8.01156C111.734 8.01156 112.613 8.25296 113.278 8.73577C113.943 9.21858 114.334 9.90055 114.451 10.7817H112.555C112.503 10.3713 112.307 10.0575 111.968 9.8402C111.63 9.61086 111.18 9.4962 110.62 9.4962C110.059 9.4962 109.629 9.59879 109.33 9.80399C109.03 10.0092 108.88 10.2808 108.88 10.6187C108.88 10.836 108.965 11.0291 109.134 11.1981C109.303 11.3671 109.551 11.5119 109.877 11.6326C110.216 11.7413 110.626 11.8439 111.108 11.9404C111.799 12.0611 112.418 12.212 112.965 12.3931C113.513 12.5741 113.949 12.8396 114.275 13.1897C114.601 13.5397 114.764 14.0406 114.764 14.6924C114.777 15.2597 114.62 15.7606 114.294 16.1952C113.982 16.6297 113.532 16.9677 112.946 17.2091C112.372 17.4505 111.688 17.5712 110.893 17.5712Z" fill="white"></path>
                </svg>
            </a>
            <div class="nav-cta-group">
                <nav class="landing-nav-items">
                    <a class="nav-link active-link" href="#">Home</a>
                    <a class="nav-link" href="#">Docs</a>
                    <a class="nav-link" href="#">Showcase</a>
                </nav>
                <button class="cta-button">
                    Star On GitHub
                    <span>
                        <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2.52894 2.0306L3.11194 0.794686C3.12764 0.759297 3.15328 0.729227 3.18574 0.708121C3.2182 0.687015 3.25608 0.675781 3.2948 0.675781C3.33352 0.675781 3.3714 0.687015 3.40386 0.708121C3.43632 0.729227 3.46196 0.759297 3.47766 0.794686L4.06066 2.0306L5.36388 2.22992C5.53104 2.25535 5.59761 2.47074 5.47682 2.59415L4.53371 3.55521L4.75621 4.91303C4.785 5.08767 4.60999 5.22042 4.46041 5.13815L3.2948 4.49683L2.12919 5.13815C1.97961 5.22042 1.8046 5.08767 1.83339 4.91303L2.05589 3.55558L1.11278 2.59415C0.991994 2.47074 1.05856 2.25535 1.22572 2.22992L2.52894 2.0306Z" fill="white" stroke="white" stroke-width="0.56093" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        19.4K
                    </span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <section class="landing-wrapper">
        <canvas class="background-canvas" id="backgroundCanvas"></canvas>

        <div class="landing-content">
            <!-- Hero Section -->
            <div class="hero-main-content">
                <h1 class="landing-title">
                    <span class="hero-split">Animated React components</span>
                    <br>
                    <span class="hero-split">for creative developers</span>
                </h1>
                <p class="landing-subtitle">
                    Ninety-plus snippets, ready to be dropped into your React projects
                </p>
                <a class="landing-button" href="#">
                    <span>Browse Components</span>
                    <div class="button-arrow-circle">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="#ffffff" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 12L10 8L6 4" stroke="#4c1d95" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </a>
            </div>

            <!-- Hero Cards -->
            <div class="hero-cards-container">
                <div class="hero-card hero-card-1">
                    <canvas id="dotGridCanvas" width="400" height="150"></canvas>
                </div>
                <div class="hero-cards-row">
                    <div class="hero-card hero-card-2">
                        <canvas id="glitchCanvas" width="400" height="200"></canvas>
                    </div>
                    <div class="hero-card hero-card-3">
                        <canvas id="squaresCanvas" width="300" height="150"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <div class="features-section">
        <div class="features-container">
            <div class="features-header">
                <h3 class="features-title">Zero cost, all the cool.</h3>
                <p class="features-subtitle">Everything you need to add flair to your websites</p>
            </div>
            <div class="bento-grid">
                <div class="feature-card card1">
                    <img alt="Messages animation" class="messages-gif" src="https://reactbits.dev/assets/messages.gif">
                    <h2><span>100</span>%</h2>
                    <h3>Free & Open Source</h3>
                    <p>Loved by developers around the world</p>
                </div>
                <div class="feature-card card2">
                    <img alt="Components animation" class="components-gif" src="https://reactbits.dev/assets/components.gif">
                    <h2><span>90</span>+</h2>
                    <h3>Curated Components</h3>
                    <p>Growing weekly & only getting better</p>
                </div>
                <div class="feature-card card4">
                    <img alt="Switch animation" class="switch-gif" src="https://reactbits.dev/assets/switch.gif">
                    <h2><span>2</span></h2>
                    <h3>Styling Options</h3>
                    <p>CSS or Tailwind, switch with one click</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Testimonials Section -->
    <section class="testimonials-section">
        <div class="testimonials-container">
            <div class="testimonials-header">
                <h3 class="testimonials-title">Loved by devs worldwide</h3>
                <p class="testimonials-subtitle">See what developers are saying about React Bits</p>
            </div>
            <div class="testimonials-marquee-container">
                <div class="testimonial-row">
                    <div class="testimonial-marquee testimonial-marquee-left">
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <p class="testimonial-text">Saw a lot of UI components & libraries but reactbits.dev has it all</p>
                                <div class="testimonial-author">
                                    <img alt="Avatar" class="testimonial-avatar" src="https://pbs.twimg.com/profile_images/1923072273809801216/B2K1_X63_400x400.jpg">
                                    <span class="testimonial-handle">@itsRajnandinit</span>
                                </div>
                            </div>
                        </div>
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <p class="testimonial-text">Just discovered http://reactbits.dev — a sleek, minimal, and super dev-friendly React component library. Clean UI, easy to use, and perfect for modern projects.</p>
                                <div class="testimonial-author">
                                    <img alt="Avatar" class="testimonial-avatar" src="https://pbs.twimg.com/profile_images/1918646280223608832/nqBF4zh__400x400.jpg">
                                    <span class="testimonial-handle">@syskey_dmg</span>
                                </div>
                            </div>
                        </div>
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <p class="testimonial-text">Really impressed by https://reactbits.dev. Check it out. The Splash Cursor effect is amazing.</p>
                                <div class="testimonial-author">
                                    <img alt="Avatar" class="testimonial-avatar" src="https://pbs.twimg.com/profile_images/1794450494686932992/wqRqF4dt_400x400.jpg">
                                    <span class="testimonial-handle">@makwanadeepam</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="testimonial-row">
                    <div class="testimonial-marquee testimonial-marquee-right">
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <p class="testimonial-text">React Bits: A stellar collection of React components to make your landing pages shine ✨</p>
                                <div class="testimonial-author">
                                    <img alt="Avatar" class="testimonial-avatar" src="https://pbs.twimg.com/profile_images/1722358890807861248/75S7CB3G_400x400.jpg">
                                    <span class="testimonial-handle">@gregberge_</span>
                                </div>
                            </div>
                        </div>
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <p class="testimonial-text">Literally the coolest react library in react -</p>
                                <div class="testimonial-author">
                                    <img alt="Avatar" class="testimonial-avatar" src="https://pbs.twimg.com/profile_images/1554006663853592576/Gxtolzbo_400x400.jpg">
                                    <span class="testimonial-handle">@Logreg_n_coffee</span>
                                </div>
                            </div>
                        </div>
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <p class="testimonial-text">Have you heard of react bits? David Haz has lovingly put together a collection of animated and fully customizable React components.</p>
                                <div class="testimonial-author">
                                    <img alt="Avatar" class="testimonial-avatar" src="https://pbs.twimg.com/profile_images/1880284612062056448/4Y2C8Xnv_400x400.jpg">
                                    <span class="testimonial-handle">@DIYDevs</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="testimonial-row">
                    <div class="testimonial-marquee testimonial-marquee-left">
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <p class="testimonial-text">React Bits has got to be the most artistic ui component lib I have seen in a while 🤌</p>
                                <div class="testimonial-author">
                                    <img alt="Avatar" class="testimonial-avatar" src="https://pbs.twimg.com/profile_images/1724192049002340352/-tood-4D_400x400.jpg">
                                    <span class="testimonial-handle">@GibsonSMurray</span>
                                </div>
                            </div>
                        </div>
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <p class="testimonial-text">This React library is absolutely amazing!!! React Bits is packed with animated components that make building creative and interactive websites so much easier.</p>
                                <div class="testimonial-author">
                                    <img alt="Avatar" class="testimonial-avatar" src="https://pbs.twimg.com/profile_images/1885430699567513600/JP1m8cHY_400x400.jpg">
                                    <span class="testimonial-handle">@Traccey001</span>
                                </div>
                            </div>
                        </div>
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <p class="testimonial-text">Today, I explored React Bit Animation, a lightweight library to add beautiful animations to your React apps! It's super easy to use and helps make UIs feel much more dynamic and interactive ✨</p>
                                <div class="testimonial-author">
                                    <img alt="Avatar" class="testimonial-avatar" src="https://pbs.twimg.com/profile_images/1915754015381483520/07SpEJWa_400x400.jpg">
                                    <span class="testimonial-handle">@Alishahzad2000M</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Start Building Section -->
    <section class="start-building-section">
        <div class="start-building-container">
            <div class="start-building-card">
                <h2 class="start-building-title">Start exploring React Bits</h2>
                <p class="start-building-subtitle">Animations, components, backgrounds - it's all here</p>
                <a class="start-building-button" href="#">Browse Components</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="landing-footer">
        <div class="footer-content">
            <div class="footer-left">
                <img alt="React Bits" class="footer-logo" src="https://reactbits.dev/assets/react-bits-logo-BEVRCkxh.svg">
                <p class="footer-description">
                    A library created with
                    <svg class="footer-heart" stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                        <path d="M923 283.6a260.04 260.04 0 0 0-56.9-82.8 264.4 264.4 0 0 0-84-55.5A265.34 265.34 0 0 0 679.7 125c-49.3 0-97.4 13.5-139.2 39-10 6.1-19.5 12.8-28.5 20.1-9-7.3-18.5-14-28.5-20.1-41.8-25.5-89.9-39-139.2-39-35.5 0-69.9 6.8-102.4 20.3-31.4 13-59.7 31.7-84 55.5a258.44 258.44 0 0 0-56.9 82.8c-13.9 32.3-21 66.6-21 101.9 0 33.3 6.8 68 20.3 103.3 11.3 29.5 27.5 60.1 48.2 91 32.8 48.9 77.9 99.9 133.9 151.6 92.8 85.7 184.7 144.9 188.6 147.3l23.7 15.2c10.5 6.7 24 6.7 34.5 0l23.7-15.2c3.9-2.5 95.7-61.6 188.6-147.3 56-51.7 101.1-102.7 133.9-151.6 20.7-30.9 37-61.5 48.2-91 13.5-35.3 20.3-70 20.3-103.3.1-35.3-7-69.6-20.9-101.9z"/>
                    </svg>
                    by <a href="#" class="footer-creator-link">this guy</a>
                </p>
                <p class="footer-copyright">© 2025 React Bits</p>
            </div>
            <div class="footer-links">
                <a href="#" class="footer-link">Vue Bits</a>
                <a href="#" class="footer-link">GitHub</a>
                <a href="#" class="footer-link">Docs</a>
                <a href="#" class="footer-link">Showcase</a>
                <a href="#" class="footer-link">CLI</a>
            </div>
        </div>
    </footer>

    <!-- Global Spotlight Effect -->
    <div class="global-spotlight" id="spotlight"></div>

    <script>
        // Background Canvas Animation
        function initBackgroundCanvas() {
            const canvas = document.getElementById('backgroundCanvas');
            const ctx = canvas.getContext('2d');

            function resizeCanvas() {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            }

            resizeCanvas();
            window.addEventListener('resize', resizeCanvas);

            const particles = [];
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 0.5,
                    vy: (Math.random() - 0.5) * 0.5,
                    size: Math.random() * 2 + 1,
                    opacity: Math.random() * 0.5 + 0.2
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                particles.forEach(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;

                    if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fillStyle = `rgba(132, 0, 255, ${particle.opacity})`;
                    ctx.fill();
                });

                requestAnimationFrame(animate);
            }

            animate();
        }

        // Dot Grid Canvas
        function initDotGrid() {
            const canvas = document.getElementById('dotGridCanvas');
            const ctx = canvas.getContext('2d');
            const spacing = 20;
            const dotSize = 2;

            function drawDots() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                for (let x = spacing; x < canvas.width; x += spacing) {
                    for (let y = spacing; y < canvas.height; y += spacing) {
                        const distance = Math.sqrt(Math.pow(x - canvas.width/2, 2) + Math.pow(y - canvas.height/2, 2));
                        const opacity = Math.max(0, 1 - distance / 100);

                        ctx.beginPath();
                        ctx.arc(x, y, dotSize, 0, Math.PI * 2);
                        ctx.fillStyle = `rgba(255, 255, 255, ${opacity * 0.3})`;
                        ctx.fill();
                    }
                }
            }

            drawDots();
        }

        // Glitch Canvas
        function initGlitchCanvas() {
            const canvas = document.getElementById('glitchCanvas');
            const ctx = canvas.getContext('2d');

            function glitchEffect() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                for (let i = 0; i < 10; i++) {
                    const x = Math.random() * canvas.width;
                    const y = Math.random() * canvas.height;
                    const width = Math.random() * 100 + 20;
                    const height = Math.random() * 5 + 1;

                    ctx.fillStyle = `rgba(132, 0, 255, ${Math.random() * 0.5})`;
                    ctx.fillRect(x, y, width, height);
                }

                setTimeout(glitchEffect, 100 + Math.random() * 200);
            }

            glitchEffect();
        }

        // Squares Canvas
        function initSquaresCanvas() {
            const canvas = document.getElementById('squaresCanvas');
            const ctx = canvas.getContext('2d');
            const squares = [];

            for (let i = 0; i < 20; i++) {
                squares.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    size: Math.random() * 20 + 5,
                    rotation: Math.random() * Math.PI * 2,
                    rotationSpeed: (Math.random() - 0.5) * 0.02,
                    opacity: Math.random() * 0.3 + 0.1
                });
            }

            function animateSquares() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                squares.forEach(square => {
                    square.rotation += square.rotationSpeed;

                    ctx.save();
                    ctx.translate(square.x, square.y);
                    ctx.rotate(square.rotation);
                    ctx.fillStyle = `rgba(132, 0, 255, ${square.opacity})`;
                    ctx.fillRect(-square.size/2, -square.size/2, square.size, square.size);
                    ctx.restore();
                });

                requestAnimationFrame(animateSquares);
            }

            animateSquares();
        }

        // Global Spotlight Effect
        function initSpotlight() {
            const spotlight = document.getElementById('spotlight');

            document.addEventListener('mousemove', (e) => {
                spotlight.style.left = e.clientX + 'px';
                spotlight.style.top = e.clientY + 'px';
                spotlight.style.opacity = '1';
            });

            document.addEventListener('mouseleave', () => {
                spotlight.style.opacity = '0';
            });
        }

        // Initialize all animations
        document.addEventListener('DOMContentLoaded', () => {
            initBackgroundCanvas();
            initDotGrid();
            initGlitchCanvas();
            initSquaresCanvas();
            initSpotlight();
        });
    </script>

    <style>
        .global-spotlight {
            position: fixed;
            width: 800px;
            height: 800px;
            border-radius: 50%;
            pointer-events: none;
            background: radial-gradient(circle, rgba(132, 0, 255, 0.15) 0%, rgba(132, 0, 255, 0.08) 15%, rgba(132, 0, 255, 0.04) 25%, rgba(132, 0, 255, 0.02) 40%, rgba(132, 0, 255, 0.01) 65%, transparent 70%);
            z-index: 200;
            opacity: 0;
            transform: translate(-50%, -50%);
            mix-blend-mode: screen;
            transition: opacity 0.3s ease;
        }
    </style>
</body>
</html>